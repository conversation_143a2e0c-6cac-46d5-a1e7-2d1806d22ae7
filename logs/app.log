{"timestamp": "2025-07-06T19:24:57.800751Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:24:57.802984Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:24:57.803424Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:24:57.803667Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.804917Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:24:57.805298Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.806337Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:24:57.806557Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.807369Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:24:57.807740Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.808672Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:24:57.809019Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.809837Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:24:57.810038Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.810552Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:24:57.810734Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.811343Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:24:57.811526Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.813523Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE users (\n\tid INTEGER NOT NULL, \n\tusername VARCHAR(50) NOT NULL, \n\temail VARCHAR(100) NOT NULL, \n\thashed_password VARCHAR(255) NOT NULL, \n\tavatar_url TEXT, \n\tis_online BOOLEAN, \n\tis_active BOOLEAN, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tlast_seen DATETIME, \n\tPRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.813765Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.828047Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX ix_users_username ON users (username)"}
{"timestamp": "2025-07-06T19:24:57.828398Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00026s] ()"}
{"timestamp": "2025-07-06T19:24:57.844769Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX ix_users_email ON users (email)"}
{"timestamp": "2025-07-06T19:24:57.845029Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.864117Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_users_id ON users (id)"}
{"timestamp": "2025-07-06T19:24:57.864424Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00031s] ()"}
{"timestamp": "2025-07-06T19:24:57.879795Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE rooms (\n\tid INTEGER NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\troom_type VARCHAR(7) NOT NULL, \n\tis_active BOOLEAN, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tcreated_by INTEGER NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.880188Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00059s] ()"}
{"timestamp": "2025-07-06T19:24:57.894269Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_rooms_name ON rooms (name)"}
{"timestamp": "2025-07-06T19:24:57.894550Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00032s] ()"}
{"timestamp": "2025-07-06T19:24:57.908213Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_rooms_id ON rooms (id)"}
{"timestamp": "2025-07-06T19:24:57.908506Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00038s] ()"}
{"timestamp": "2025-07-06T19:24:57.936604Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE messages (\n\tid INTEGER NOT NULL, \n\tcontent TEXT NOT NULL, \n\tmessage_type VARCHAR(6) NOT NULL, \n\tstatus VARCHAR(9) NOT NULL, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tsender_id INTEGER NOT NULL, \n\trecipient_id INTEGER, \n\troom_id INTEGER, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(sender_id) REFERENCES users (id), \n\tFOREIGN KEY(recipient_id) REFERENCES users (id), \n\tFOREIGN KEY(room_id) REFERENCES rooms (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.936923Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00028s] ()"}
{"timestamp": "2025-07-06T19:24:57.956842Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_messages_id ON messages (id)"}
{"timestamp": "2025-07-06T19:24:57.957109Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.970350Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE room_members (\n\tid INTEGER NOT NULL, \n\trole VARCHAR(9) NOT NULL, \n\tjoined_at DATETIME, \n\tis_active BOOLEAN, \n\tuser_id INTEGER NOT NULL, \n\troom_id INTEGER NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(user_id) REFERENCES users (id), \n\tFOREIGN KEY(room_id) REFERENCES rooms (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.970683Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00028s] ()"}
{"timestamp": "2025-07-06T19:24:57.983293Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_room_members_id ON room_members (id)"}
{"timestamp": "2025-07-06T19:24:57.983469Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00021s] ()"}
{"timestamp": "2025-07-06T19:24:57.995792Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:24:57.996192Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:24:57.997166Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:28:00.461785Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:00.484884Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:28:00.485216Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00049s] ('testuser',)"}
{"timestamp": "2025-07-06T19:28:00.488021Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:28:00.488295Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00050s] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:28:00.793098Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T19:28:00.793392Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00038s] ('testuser', '<EMAIL>', '$2b$12$ooFIKelVuasF6bYKGa2M7OgSHNlJ0kAUtcGPk6rw3wgcj.VyE.IWK', None, 0, 1, '2025-07-06 19:28:00.792923', '2025-07-06 19:28:00.792926', '2025-07-06 19:28:00.792926')"}
{"timestamp": "2025-07-06T19:28:00.794801Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:28:00.830709Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:00.832562Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T19:28:00.833039Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00053s] (1,)"}
{"timestamp": "2025-07-06T19:28:00.834357Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:28:00.835448Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:28:31.369463Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:31.370141Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:28:31.370603Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 30.89s ago] ('testuser',)"}
{"timestamp": "2025-07-06T19:28:31.651745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:28:31.652059Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00051s] (1, '2025-07-06 19:28:31.651527', '2025-07-06 19:28:31.649297', 1)"}
{"timestamp": "2025-07-06T19:28:31.653230Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:28:31.661243Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:02.307537Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:02.308076Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:35:02.308274Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 421.8s ago] ('mazen',)"}
{"timestamp": "2025-07-06T19:35:02.309589Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:35:02.309796Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 421.8s ago] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:35:02.310490Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: RegistrationFailed", "event": "RegistrationFailed", "user": "mazen", "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:02.310779Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:35:22.883847Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:22.884384Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:35:22.884658Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('mazen',)"}
{"timestamp": "2025-07-06T19:35:22.886292Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:35:22.886623Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:35:23.166593Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T19:35:23.166872Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('mazen', '<EMAIL>', '$2b$12$CenU6vVKidt04lDUDz1ceeL8JbHh65Igt4P8cSBjRD0TL2cC5g8.S', None, 0, 1, '2025-07-06 19:35:23.166442', '2025-07-06 19:35:23.166444', '2025-07-06 19:35:23.166444')"}
{"timestamp": "2025-07-06T19:35:23.168043Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:35:23.188650Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:23.189221Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T19:35:23.189443Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] (2,)"}
{"timestamp": "2025-07-06T19:35:23.190637Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:23.191925Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:45:12.338817Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:45:17.669373Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:45:17.671794Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:45:17.672192Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:45:17.672430Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.673552Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:45:17.673828Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.674659Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:45:17.674905Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.675654Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:45:17.675901Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.676996Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:45:17.677285Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:45:17.677987Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:46:05.616281Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:46:18.488013Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:46:18.490936Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:46:18.491417Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:46:18.491722Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.492863Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:46:18.493031Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.493588Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:46:18.493745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.494527Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:46:18.494671Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.495600Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:46:18.495848Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:46:18.496656Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:48:39.586892Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:48:39.614496Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:48:39.614779Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00049s] ('testuser',)"}
{"timestamp": "2025-07-06T19:48:39.928909Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:48:39.929169Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00046s] ('2025-07-06 19:48:39.928609', '2025-07-06 19:48:39.926584', 1)"}
{"timestamp": "2025-07-06T19:48:39.930885Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:48:39.950024Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:49:12.212098Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:49:17.437174Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:49:17.439459Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:49:17.445374Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:49:17.445598Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.446895Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:49:17.447094Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.447957Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:49:17.448163Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.448954Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:49:17.449143Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.450030Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:49:17.450246Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:49:17.450964Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:49:55.957583Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:49:55.979571Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:49:55.979781Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00032s] ('mazen',)"}
{"timestamp": "2025-07-06T19:49:56.282884Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:49:56.283201Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00042s] (1, '2025-07-06 19:49:56.282618', '2025-07-06 19:49:56.280964', 2)"}
{"timestamp": "2025-07-06T19:49:56.285047Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:49:56.304135Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
