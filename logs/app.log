{"timestamp": "2025-07-06T19:24:57.800751Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:24:57.802984Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:24:57.803424Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:24:57.803667Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.804917Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:24:57.805298Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.806337Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:24:57.806557Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.807369Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:24:57.807740Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.808672Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:24:57.809019Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.809837Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:24:57.810038Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.810552Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:24:57.810734Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.811343Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA temp.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:24:57.811526Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:24:57.813523Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE users (\n\tid INTEGER NOT NULL, \n\tusername VARCHAR(50) NOT NULL, \n\temail VARCHAR(100) NOT NULL, \n\thashed_password VARCHAR(255) NOT NULL, \n\tavatar_url TEXT, \n\tis_online BOOLEAN, \n\tis_active BOOLEAN, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tlast_seen DATETIME, \n\tPRIMARY KEY (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.813765Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.828047Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX ix_users_username ON users (username)"}
{"timestamp": "2025-07-06T19:24:57.828398Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00026s] ()"}
{"timestamp": "2025-07-06T19:24:57.844769Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE UNIQUE INDEX ix_users_email ON users (email)"}
{"timestamp": "2025-07-06T19:24:57.845029Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.864117Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_users_id ON users (id)"}
{"timestamp": "2025-07-06T19:24:57.864424Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00031s] ()"}
{"timestamp": "2025-07-06T19:24:57.879795Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE rooms (\n\tid INTEGER NOT NULL, \n\tname VARCHAR(100) NOT NULL, \n\tdescription TEXT, \n\troom_type VARCHAR(7) NOT NULL, \n\tis_active BOOLEAN, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tcreated_by INTEGER NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(created_by) REFERENCES users (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.880188Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00059s] ()"}
{"timestamp": "2025-07-06T19:24:57.894269Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_rooms_name ON rooms (name)"}
{"timestamp": "2025-07-06T19:24:57.894550Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00032s] ()"}
{"timestamp": "2025-07-06T19:24:57.908213Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_rooms_id ON rooms (id)"}
{"timestamp": "2025-07-06T19:24:57.908506Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00038s] ()"}
{"timestamp": "2025-07-06T19:24:57.936604Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE messages (\n\tid INTEGER NOT NULL, \n\tcontent TEXT NOT NULL, \n\tmessage_type VARCHAR(6) NOT NULL, \n\tstatus VARCHAR(9) NOT NULL, \n\tcreated_at DATETIME, \n\tupdated_at DATETIME, \n\tsender_id INTEGER NOT NULL, \n\trecipient_id INTEGER, \n\troom_id INTEGER, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(sender_id) REFERENCES users (id), \n\tFOREIGN KEY(recipient_id) REFERENCES users (id), \n\tFOREIGN KEY(room_id) REFERENCES rooms (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.936923Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00028s] ()"}
{"timestamp": "2025-07-06T19:24:57.956842Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_messages_id ON messages (id)"}
{"timestamp": "2025-07-06T19:24:57.957109Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00033s] ()"}
{"timestamp": "2025-07-06T19:24:57.970350Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "\nCREATE TABLE room_members (\n\tid INTEGER NOT NULL, \n\trole VARCHAR(9) NOT NULL, \n\tjoined_at DATETIME, \n\tis_active BOOLEAN, \n\tuser_id INTEGER NOT NULL, \n\troom_id INTEGER NOT NULL, \n\tPRIMARY KEY (id), \n\tFOREIGN KEY(user_id) REFERENCES users (id), \n\tFOREIGN KEY(room_id) REFERENCES rooms (id)\n)\n\n"}
{"timestamp": "2025-07-06T19:24:57.970683Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00028s] ()"}
{"timestamp": "2025-07-06T19:24:57.983293Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "CREATE INDEX ix_room_members_id ON room_members (id)"}
{"timestamp": "2025-07-06T19:24:57.983469Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[no key 0.00021s] ()"}
{"timestamp": "2025-07-06T19:24:57.995792Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:24:57.996192Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:24:57.997166Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:28:00.461785Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:00.484884Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:28:00.485216Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00049s] ('testuser',)"}
{"timestamp": "2025-07-06T19:28:00.488021Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:28:00.488295Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00050s] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:28:00.793098Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T19:28:00.793392Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00038s] ('testuser', '<EMAIL>', '$2b$12$ooFIKelVuasF6bYKGa2M7OgSHNlJ0kAUtcGPk6rw3wgcj.VyE.IWK', None, 0, 1, '2025-07-06 19:28:00.792923', '2025-07-06 19:28:00.792926', '2025-07-06 19:28:00.792926')"}
{"timestamp": "2025-07-06T19:28:00.794801Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:28:00.830709Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:00.832562Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T19:28:00.833039Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00053s] (1,)"}
{"timestamp": "2025-07-06T19:28:00.834357Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:28:00.835448Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:28:31.369463Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:28:31.370141Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:28:31.370603Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 30.89s ago] ('testuser',)"}
{"timestamp": "2025-07-06T19:28:31.651745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:28:31.652059Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00051s] (1, '2025-07-06 19:28:31.651527', '2025-07-06 19:28:31.649297', 1)"}
{"timestamp": "2025-07-06T19:28:31.653230Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:28:31.661243Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:02.307537Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:02.308076Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:35:02.308274Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 421.8s ago] ('mazen',)"}
{"timestamp": "2025-07-06T19:35:02.309589Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:35:02.309796Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 421.8s ago] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:35:02.310490Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: RegistrationFailed", "event": "RegistrationFailed", "user": "mazen", "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:02.310779Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:35:22.883847Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:22.884384Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:35:22.884658Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('mazen',)"}
{"timestamp": "2025-07-06T19:35:22.886292Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T19:35:22.886623Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T19:35:23.166593Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T19:35:23.166872Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] ('mazen', '<EMAIL>', '$2b$12$CenU6vVKidt04lDUDz1ceeL8JbHh65Igt4P8cSBjRD0TL2cC5g8.S', None, 0, 1, '2025-07-06 19:35:23.166442', '2025-07-06 19:35:23.166444', '2025-07-06 19:35:23.166444')"}
{"timestamp": "2025-07-06T19:35:23.168043Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:35:23.188650Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:35:23.189221Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T19:35:23.189443Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 442.4s ago] (2,)"}
{"timestamp": "2025-07-06T19:35:23.190637Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:35:23.191925Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T19:45:12.338817Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:45:17.669373Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:45:17.671794Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:45:17.672192Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:45:17.672430Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.673552Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:45:17.673828Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.674659Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:45:17.674905Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.675654Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:45:17.675901Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:45:17.676996Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:45:17.677285Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:45:17.677987Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:46:05.616281Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:46:18.488013Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:46:18.490936Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:46:18.491417Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:46:18.491722Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.492863Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:46:18.493031Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.493588Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:46:18.493745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.494527Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:46:18.494671Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:46:18.495600Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:46:18.495848Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:46:18.496656Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:48:39.586892Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:48:39.614496Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:48:39.614779Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00049s] ('testuser',)"}
{"timestamp": "2025-07-06T19:48:39.928909Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:48:39.929169Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00046s] ('2025-07-06 19:48:39.928609', '2025-07-06 19:48:39.926584', 1)"}
{"timestamp": "2025-07-06T19:48:39.930885Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:48:39.950024Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:49:12.212098Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:49:17.437174Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:49:17.439459Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:49:17.445374Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:49:17.445598Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.446895Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:49:17.447094Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.447957Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:49:17.448163Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.448954Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:49:17.449143Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:49:17.450030Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:49:17.450246Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:49:17.450964Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:49:55.957583Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:49:55.979571Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:49:55.979781Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00032s] ('mazen',)"}
{"timestamp": "2025-07-06T19:49:56.282884Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:49:56.283201Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00042s] (1, '2025-07-06 19:49:56.282618', '2025-07-06 19:49:56.280964', 2)"}
{"timestamp": "2025-07-06T19:49:56.285047Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:49:56.304135Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:52:28.726336Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:52:30.032512Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:52:30.035042Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:52:30.035474Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:52:30.035718Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:52:30.037043Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:52:30.037360Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:52:30.038232Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:52:30.038490Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:52:30.039167Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:52:30.039376Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:52:30.040454Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:52:30.040685Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:52:30.041379Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:52:48.587963Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:54:58.238478Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:54:58.240762Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:54:58.241196Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:54:58.241437Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:54:58.242870Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:54:58.243150Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:54:58.244179Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:54:58.244440Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:54:58.246023Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:54:58.246289Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:54:58.249206Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:54:58.249487Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:54:58.250306Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:55:17.900222Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:55:26.327699Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:55:26.330044Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:55:26.330491Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:55:26.330758Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:55:26.332385Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:55:26.332744Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:55:26.333635Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:55:26.333872Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:55:26.334776Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:55:26.334995Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:55:26.336145Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:55:26.336371Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:55:26.336942Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T19:55:41.023923Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:55:41.048429Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:55:41.048648Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00028s] ('testuser',)"}
{"timestamp": "2025-07-06T19:55:41.353972Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:55:41.354175Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00042s] ('2025-07-06 19:55:41.353778', '2025-07-06 19:55:41.351963', 1)"}
{"timestamp": "2025-07-06T19:55:41.355834Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:55:41.367112Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "testuser", "user_id": 1, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:56:46.007689Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:56:46.008336Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T19:56:46.008566Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 64.96s ago] ('mazen',)"}
{"timestamp": "2025-07-06T19:56:46.289558Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T19:56:46.289921Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 64.94s ago] ('2025-07-06 19:56:46.289417', '2025-07-06 19:56:46.288985', 2)"}
{"timestamp": "2025-07-06T19:56:46.291078Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:56:46.310107Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T19:59:51.602251Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T19:59:52.729018Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T19:59:52.732472Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T19:59:52.732848Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T19:59:52.733030Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:59:52.735893Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T19:59:52.736320Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:59:52.737545Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T19:59:52.738059Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:59:52.739122Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T19:59:52.739329Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T19:59:52.741024Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T19:59:52.741446Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T19:59:52.742418Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:00:05.075167Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T20:00:06.171963Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T20:00:06.174692Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:00:06.175058Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T20:00:06.175259Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:00:06.176309Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T20:00:06.176447Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:00:06.177227Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T20:00:06.177465Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:00:06.178195Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T20:00:06.178373Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:00:06.179343Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T20:00:06.179610Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:00:06.180219Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:20:08.816861Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:20:08.836796Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:20:08.836971Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00033s] ('ali',)"}
{"timestamp": "2025-07-06T20:20:08.838894Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T20:20:08.839048Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00025s] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T20:20:09.137880Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:20:09.138212Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00045s] ('ali', '<EMAIL>', '$2b$12$4tmkINDDElRE.eun2reLl.Eae5jD4nX4EdcvT3MzYAsIyht2OHKL2', None, 0, 1, '2025-07-06 20:20:09.137622', '2025-07-06 20:20:09.137625', '2025-07-06 20:20:09.137625')"}
{"timestamp": "2025-07-06T20:20:09.139757Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:20:09.185877Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:20:09.187385Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:20:09.187606Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00028s] (3,)"}
{"timestamp": "2025-07-06T20:20:09.188952Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "ali", "user_id": 3, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:20:09.189774Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:20:37.710575Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:20:37.711178Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:20:37.711418Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 28.87s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:20:37.993902Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T20:20:37.994227Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00060s] (1, '2025-07-06 20:20:37.993614', '2025-07-06 20:20:37.991802', 3)"}
{"timestamp": "2025-07-06T20:20:37.996205Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:20:38.015164Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "ali", "user_id": 3, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:21:13.410945Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:21:13.411417Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:21:13.411618Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 64.57s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:21:13.414015Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:21:54.582552Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:21:54.583146Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:21:54.583448Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 105.7s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:21:54.586733Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT rooms.id, rooms.name, rooms.description, rooms.room_type, rooms.is_active, rooms.created_at, rooms.updated_at, rooms.created_by \nFROM rooms \nWHERE rooms.name = ?"}
{"timestamp": "2025-07-06T20:21:54.587033Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00036s] ('ali',)"}
{"timestamp": "2025-07-06T20:21:54.589738Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO rooms (name, description, room_type, is_active, created_at, updated_at, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:21:54.590003Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00044s] ('ali', 'alinaser', 'PUBLIC', 1, '2025-07-06 20:21:54.589500', '2025-07-06 20:21:54.589502', 3)"}
{"timestamp": "2025-07-06T20:21:54.593275Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO room_members (role, joined_at, is_active, user_id, room_id) VALUES (?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:21:54.593559Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00048s] ('ADMIN', '2025-07-06 20:21:54.593018', 1, 3, 1)"}
{"timestamp": "2025-07-06T20:21:54.596046Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:21:54.616204Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:21:54.617346Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT rooms.id, rooms.name, rooms.description, rooms.room_type, rooms.is_active, rooms.created_at, rooms.updated_at, rooms.created_by \nFROM rooms \nWHERE rooms.id = ?"}
{"timestamp": "2025-07-06T20:21:54.617667Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00031s] (1,)"}
{"timestamp": "2025-07-06T20:21:54.619236Z", "level": "INFO", "logger": "chat.rooms", "message": "Room action: RoomCreated", "event": "RoomCreated", "user_id": 3, "room_id": 1}
{"timestamp": "2025-07-06T20:21:54.620281Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:23:31.990467Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:23:31.991009Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:23:31.991176Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 203.2s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:23:31.993602Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:23:31.993858Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00031s] (3,)"}
{"timestamp": "2025-07-06T20:23:31.995162Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:23:52.011970Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:23:52.012508Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:23:52.012778Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 223.2s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:23:52.014499Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:23:52.014753Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 20.02s ago] (1,)"}
{"timestamp": "2025-07-06T20:23:52.018288Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO messages (content, message_type, status, created_at, updated_at, sender_id, recipient_id, room_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:23:52.018576Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00064s] ('mazen mahmoud', 'DIRECT', 'SENT', '2025-07-06 20:23:52.017899', '2025-07-06 20:23:52.017902', 3, 1, 1)"}
{"timestamp": "2025-07-06T20:23:52.020063Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:23:52.040548Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:23:52.042117Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.id = ?"}
{"timestamp": "2025-07-06T20:23:52.042372Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00034s] (1,)"}
{"timestamp": "2025-07-06T20:23:52.043871Z", "level": "INFO", "logger": "chat.messages", "message": "Message action: MessageSent", "event": "MessageSent", "user_id": 3, "recipient_id": 1, "room_id": 1, "message_id": "msg_1"}
{"timestamp": "2025-07-06T20:23:52.044897Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:24:30.029470Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:24:30.029902Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:24:30.030079Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 261.2s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:24:30.034683Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:24:30.035024Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00053s] ('DIRECT', 3, 1, 1, 3)"}
{"timestamp": "2025-07-06T20:24:30.038606Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:24:30.038976Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00050s] ('DIRECT', 3, 1, 1, 3, 50, 0)"}
{"timestamp": "2025-07-06T20:24:30.041226Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:25:00.118812Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:25:00.119366Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:25:00.119595Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 291.3s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:25:00.123102Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT rooms.id, rooms.name, rooms.description, rooms.room_type, rooms.is_active, rooms.created_at, rooms.updated_at, rooms.created_by \nFROM rooms JOIN room_members ON rooms.id = room_members.room_id \nWHERE room_members.user_id = ? AND room_members.is_active = 1 AND rooms.is_active = 1 ORDER BY rooms.name"}
{"timestamp": "2025-07-06T20:25:00.123384Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00045s] (3,)"}
{"timestamp": "2025-07-06T20:25:00.125433Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:25:17.603832Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:25:17.604353Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:25:17.604569Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 308.8s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:25:17.608548Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT rooms.id, rooms.name, rooms.description, rooms.room_type, rooms.is_active, rooms.created_at, rooms.updated_at, rooms.created_by \nFROM rooms \nWHERE rooms.id = ? AND rooms.is_active = 1"}
{"timestamp": "2025-07-06T20:25:17.608797Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00043s] (1,)"}
{"timestamp": "2025-07-06T20:25:17.610371Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:25:51.564867Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:25:51.565341Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:25:51.565544Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 342.7s ago] ('ali',)"}
{"timestamp": "2025-07-06T20:25:51.568105Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:25:51.568365Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00040s] (1, 3)"}
{"timestamp": "2025-07-06T20:25:51.570779Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:25:51.571197Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00060s] (1,)"}
{"timestamp": "2025-07-06T20:25:51.573713Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:35:47.499301Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:35:47.499878Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:35:47.500048Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 938.7s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:35:47.783176Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T20:35:47.783494Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00032s] ('2025-07-06 20:35:47.783037', '2025-07-06 20:35:47.782145', 2)"}
{"timestamp": "2025-07-06T20:35:47.784933Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:35:47.817270Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "mazen", "user_id": 2, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:36:13.432409Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:36:13.433030Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:36:13.433265Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 964.6s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:36:13.435383Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:36:35.123767Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:36:35.124242Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:36:35.124436Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 986.3s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:36:35.126094Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT rooms.id, rooms.name, rooms.description, rooms.room_type, rooms.is_active, rooms.created_at, rooms.updated_at, rooms.created_by \nFROM rooms \nWHERE rooms.id = ? AND rooms.is_active = 1"}
{"timestamp": "2025-07-06T20:36:35.126294Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 677.5s ago] (1,)"}
{"timestamp": "2025-07-06T20:36:35.128761Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ?"}
{"timestamp": "2025-07-06T20:36:35.128987Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00041s] (1, 2)"}
{"timestamp": "2025-07-06T20:36:35.133793Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO room_members (role, joined_at, is_active, user_id, room_id) VALUES (?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:36:35.134557Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 880.5s ago] ('MEMBER', '2025-07-06 20:36:35.132571', 1, 2, 1)"}
{"timestamp": "2025-07-06T20:36:35.137271Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:36:35.161479Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:36:35.162386Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.id = ?"}
{"timestamp": "2025-07-06T20:36:35.162640Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00031s] (2,)"}
{"timestamp": "2025-07-06T20:36:35.164043Z", "level": "INFO", "logger": "chat.rooms", "message": "Room action: RoomJoined", "event": "RoomJoined", "user_id": 2, "room_id": 1}
{"timestamp": "2025-07-06T20:36:35.164924Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:36:51.171073Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:36:51.171630Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:36:51.171905Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1002s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:36:51.174061Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:36:51.174254Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 659.6s ago] (1, 2)"}
{"timestamp": "2025-07-06T20:36:51.175299Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:36:51.175480Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 659.6s ago] (1,)"}
{"timestamp": "2025-07-06T20:36:51.176752Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:37:20.019934Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:37:20.020537Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:37:20.020745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1031s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:37:20.022686Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:37:20.022888Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 770s ago] ('DIRECT', 2, 3, 3, 2)"}
{"timestamp": "2025-07-06T20:37:20.023950Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:37:20.024125Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 770s ago] ('DIRECT', 2, 3, 3, 2, 50, 0)"}
{"timestamp": "2025-07-06T20:37:20.025809Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:37:28.976220Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:37:28.976856Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:37:28.977122Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1040s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:37:28.979495Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:37:28.980201Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 778.9s ago] ('DIRECT', 2, 1, 1, 2)"}
{"timestamp": "2025-07-06T20:37:28.981911Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:37:28.982190Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 778.9s ago] ('DIRECT', 2, 1, 1, 2, 50, 0)"}
{"timestamp": "2025-07-06T20:37:28.984081Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:37:41.635561Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:37:41.636019Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:37:41.636261Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1053s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:37:41.638132Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:37:41.638433Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 710.1s ago] (1, 2)"}
{"timestamp": "2025-07-06T20:37:41.640379Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ?"}
{"timestamp": "2025-07-06T20:37:41.640641Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00032s] ('ROOM', 1)"}
{"timestamp": "2025-07-06T20:37:41.643720Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ? ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:37:41.643998Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00046s] ('ROOM', 1, 50, 0)"}
{"timestamp": "2025-07-06T20:37:41.646041Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:38:56.606141Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:38:56.606675Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:38:56.606955Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1128s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:38:56.608617Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:38:56.608826Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 924.6s ago] (2,)"}
{"timestamp": "2025-07-06T20:38:56.609580Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:39:15.512627Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:39:15.513086Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:39:15.513272Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1147s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:39:15.514748Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:39:15.514956Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 943.5s ago] (3,)"}
{"timestamp": "2025-07-06T20:39:15.516051Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO messages (content, message_type, status, created_at, updated_at, sender_id, recipient_id, room_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:39:15.516322Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 923.5s ago] ('السلام عليكم ورحمة الله وبركاته', 'DIRECT', 'SENT', '2025-07-06 20:39:15.515952', '2025-07-06 20:39:15.515953', 2, 3, 1)"}
{"timestamp": "2025-07-06T20:39:15.517686Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:39:15.559320Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:39:15.559836Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.id = ?"}
{"timestamp": "2025-07-06T20:39:15.560050Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 923.5s ago] (2,)"}
{"timestamp": "2025-07-06T20:39:15.561105Z", "level": "INFO", "logger": "chat.messages", "message": "Message action: MessageSent", "event": "MessageSent", "user_id": 2, "recipient_id": 3, "room_id": 1, "message_id": "msg_2"}
{"timestamp": "2025-07-06T20:39:15.561857Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:39:34.313817Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:39:34.314380Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:39:34.314604Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1165s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:39:34.316680Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:39:34.316862Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 904.3s ago] ('DIRECT', 2, 2, 2, 2)"}
{"timestamp": "2025-07-06T20:39:34.318146Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:39:34.318394Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 904.3s ago] ('DIRECT', 2, 2, 2, 2, 50, 0)"}
{"timestamp": "2025-07-06T20:39:34.320115Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:39:42.706427Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:39:42.706868Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:39:42.707066Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1174s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:39:42.709015Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:39:42.709199Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 912.7s ago] ('DIRECT', 2, 3, 3, 2)"}
{"timestamp": "2025-07-06T20:39:42.710155Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:39:42.710362Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 912.7s ago] ('DIRECT', 2, 3, 3, 2, 50, 0)"}
{"timestamp": "2025-07-06T20:39:42.711596Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:39:53.181643Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:39:53.182033Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:39:53.182281Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1184s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:39:53.184613Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?)"}
{"timestamp": "2025-07-06T20:39:53.185107Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 923.2s ago] ('DIRECT', 2, 1, 1, 2)"}
{"timestamp": "2025-07-06T20:39:53.186687Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND (messages.sender_id = ? AND messages.recipient_id = ? OR messages.sender_id = ? AND messages.recipient_id = ?) ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:39:53.186957Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 923.1s ago] ('DIRECT', 2, 1, 1, 2, 50, 0)"}
{"timestamp": "2025-07-06T20:39:53.188247Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:40:02.412560Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:40:02.413105Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:40:02.413282Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1194s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:40:02.415227Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:40:02.415459Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 850.8s ago] (1, 2)"}
{"timestamp": "2025-07-06T20:40:02.416958Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ?"}
{"timestamp": "2025-07-06T20:40:02.417163Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 140.8s ago] ('ROOM', 1)"}
{"timestamp": "2025-07-06T20:40:02.418422Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ? ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:40:02.418774Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 140.8s ago] ('ROOM', 1, 50, 0)"}
{"timestamp": "2025-07-06T20:40:02.420712Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:40:26.474780Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:40:26.475370Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:40:26.475611Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1218s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:40:26.477616Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:40:26.477872Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 874.9s ago] (1, 2)"}
{"timestamp": "2025-07-06T20:40:26.479853Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ?"}
{"timestamp": "2025-07-06T20:40:26.480161Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 164.8s ago] ('ROOM', 1)"}
{"timestamp": "2025-07-06T20:40:26.481948Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ? ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:40:26.482220Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 164.8s ago] ('ROOM', 1, 50, 0)"}
{"timestamp": "2025-07-06T20:40:26.483585Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:40:35.517838Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:40:35.518439Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:40:35.518684Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1227s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:40:35.521148Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:40:35.521575Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 884s ago] (2, 2)"}
{"timestamp": "2025-07-06T20:40:35.522617Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:40:45.287941Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:40:45.288476Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:40:45.288716Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1236s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:40:45.290269Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT room_members.id, room_members.role, room_members.joined_at, room_members.is_active, room_members.user_id, room_members.room_id \nFROM room_members \nWHERE room_members.room_id = ? AND room_members.user_id = ? AND room_members.is_active = 1"}
{"timestamp": "2025-07-06T20:40:45.290465Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 893.7s ago] (1, 2)"}
{"timestamp": "2025-07-06T20:40:45.291624Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(messages.id) AS count_1 \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ?"}
{"timestamp": "2025-07-06T20:40:45.291819Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 183.7s ago] ('ROOM', 1)"}
{"timestamp": "2025-07-06T20:40:45.293142Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.message_type = ? AND messages.room_id = ? ORDER BY messages.created_at DESC\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:40:45.293541Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 183.6s ago] ('ROOM', 1, 50, 0)"}
{"timestamp": "2025-07-06T20:40:45.295284Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:41:00.603719Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:41:00.604240Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:41:00.604449Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1252s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:41:00.607075Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT messages.id, messages.content, messages.message_type, messages.status, messages.created_at, messages.updated_at, messages.sender_id, messages.recipient_id, messages.room_id \nFROM messages \nWHERE messages.id = ?"}
{"timestamp": "2025-07-06T20:41:00.607432Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00052s] (1,)"}
{"timestamp": "2025-07-06T20:41:00.608769Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:41:27.966385Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:41:27.966877Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:41:27.967133Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1279s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:41:27.969407Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT count(users.id) AS count_1 \nFROM users \nWHERE users.is_active = 1"}
{"timestamp": "2025-07-06T20:41:27.969658Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00031s] ()"}
{"timestamp": "2025-07-06T20:41:27.971933Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.is_active = 1 ORDER BY users.username\n LIMIT ? OFFSET ?"}
{"timestamp": "2025-07-06T20:41:27.972185Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00041s] (50, 0)"}
{"timestamp": "2025-07-06T20:41:27.975317Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:41:59.790672Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:41:59.791207Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:41:59.791478Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1311s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:41:59.794380Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.is_online = 1 AND users.is_active = 1"}
{"timestamp": "2025-07-06T20:41:59.794746Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00044s] ()"}
{"timestamp": "2025-07-06T20:41:59.797802Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:42:42.048218Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:42:42.048639Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:42:42.048830Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1353s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:42:42.050631Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:42:42.050882Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1150s ago] (2,)"}
{"timestamp": "2025-07-06T20:42:42.052274Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:42:50.759669Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:42:50.760297Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:42:50.760523Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1362s ago] ('mazen',)"}
{"timestamp": "2025-07-06T20:42:50.762355Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:42:50.762608Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 1159s ago] (3,)"}
{"timestamp": "2025-07-06T20:42:50.763902Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:43:54.072137Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T20:43:55.426951Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T20:43:55.429325Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:43:55.429822Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T20:43:55.430054Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:55.431596Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T20:43:55.431885Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:55.432974Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T20:43:55.433249Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:55.434340Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T20:43:55.434623Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:55.435960Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T20:43:55.436200Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:43:55.436802Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:43:57.750862Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T20:43:59.062858Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T20:43:59.065827Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:43:59.066231Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T20:43:59.066460Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:59.067925Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T20:43:59.068195Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:59.069279Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T20:43:59.069493Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:59.070119Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T20:43:59.070324Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:43:59.071465Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T20:43:59.071689Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:43:59.072352Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:43:59.874372Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T20:44:01.228994Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T20:44:01.231398Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:44:01.231748Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T20:44:01.231935Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:01.232981Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T20:44:01.233124Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:01.233766Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T20:44:01.233935Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:01.234562Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T20:44:01.234751Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:01.235756Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T20:44:01.235987Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:44:01.236560Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:44:01.839293Z", "level": "INFO", "logger": "root", "message": "Shutting down Chat Application..."}
{"timestamp": "2025-07-06T20:44:03.016446Z", "level": "INFO", "logger": "root", "message": "Starting Chat Application..."}
{"timestamp": "2025-07-06T20:44:03.019052Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:44:03.019436Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"messages\")"}
{"timestamp": "2025-07-06T20:44:03.019725Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:03.020913Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"rooms\")"}
{"timestamp": "2025-07-06T20:44:03.021210Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:03.022069Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"room_members\")"}
{"timestamp": "2025-07-06T20:44:03.022341Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:03.023101Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "PRAGMA main.table_info(\"users\")"}
{"timestamp": "2025-07-06T20:44:03.023371Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[raw sql] ()"}
{"timestamp": "2025-07-06T20:44:03.025069Z", "level": "INFO", "logger": "root", "message": "Database tables created successfully"}
{"timestamp": "2025-07-06T20:44:03.025406Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:44:03.026276Z", "level": "INFO", "logger": "root", "message": "Database tables created/verified"}
{"timestamp": "2025-07-06T20:58:30.880822Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:58:30.901548Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:58:30.901814Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00034s] ('قاسم',)"}
{"timestamp": "2025-07-06T20:58:30.904349Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T20:58:30.904649Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00037s] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T20:58:31.199440Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:58:31.199780Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00038s] ('قاسم', '<EMAIL>', '$2b$12$ka38Q5EsNEXH4Fzg6QWp3OeBMe9XHDyR8OCD2HRJOcZd7pOYn4bjm', None, 0, 1, '2025-07-06 20:58:31.199263', '2025-07-06 20:58:31.199265', '2025-07-06 20:58:31.199266')"}
{"timestamp": "2025-07-06T20:58:31.201308Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:58:31.248316Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:58:31.249602Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:58:31.249833Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00024s] (4,)"}
{"timestamp": "2025-07-06T20:58:31.251050Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "قاسم", "user_id": 4, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:58:31.251873Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:58:48.746716Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:58:48.747279Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:58:48.747589Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 17.85s ago] ('قاسم',)"}
{"timestamp": "2025-07-06T20:58:49.012593Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: LoginFailed", "event": "LoginFailed", "user": "قاسم", "user_id": 4, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:58:49.013197Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:59:10.511671Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:10.512179Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:59:10.512450Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 39.61s ago] ('هادي',)"}
{"timestamp": "2025-07-06T20:59:10.513848Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.email = ?"}
{"timestamp": "2025-07-06T20:59:10.514143Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 39.61s ago] ('<EMAIL>',)"}
{"timestamp": "2025-07-06T20:59:10.781776Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "INSERT INTO users (username, email, hashed_password, avatar_url, is_online, is_active, created_at, updated_at, last_seen) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"}
{"timestamp": "2025-07-06T20:59:10.782196Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 39.58s ago] ('هادي', '<EMAIL>', '$2b$12$9pnFywvuIJUGeYXVkJnO2Oh9e61X/DalLZu/jWG/gOXkHAx/IExs6', None, 0, 1, '2025-07-06 20:59:10.781532', '2025-07-06 20:59:10.781534', '2025-07-06 20:59:10.781535')"}
{"timestamp": "2025-07-06T20:59:10.783594Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:59:10.804500Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:10.804931Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.id = ?"}
{"timestamp": "2025-07-06T20:59:10.805184Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 39.56s ago] (5,)"}
{"timestamp": "2025-07-06T20:59:10.806539Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserRegistered", "event": "UserRegistered", "user": "هادي", "user_id": 5, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:59:10.807281Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:59:25.390941Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:25.391404Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:59:25.391665Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 54.49s ago] ('هادي',)"}
{"timestamp": "2025-07-06T20:59:25.664893Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: LoginFailed", "event": "LoginFailed", "user": "هادي", "user_id": 5, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:59:25.665410Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:59:32.007000Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:32.007483Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:59:32.007757Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 61.11s ago] ('هادي',)"}
{"timestamp": "2025-07-06T20:59:32.272353Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: LoginFailed", "event": "LoginFailed", "user": "هادي", "user_id": 5, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:59:32.272755Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "ROLLBACK"}
{"timestamp": "2025-07-06T20:59:36.446745Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:36.447222Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:59:36.447433Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 65.55s ago] ('هادي',)"}
{"timestamp": "2025-07-06T20:59:36.711863Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T20:59:36.712135Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00046s] (1, '2025-07-06 20:59:36.711607', '2025-07-06 20:59:36.709943', 5)"}
{"timestamp": "2025-07-06T20:59:36.713146Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:59:36.732256Z", "level": "INFO", "logger": "chat.auth", "message": "Auth action: UserLogin", "event": "UserLogin", "user": "هادي", "user_id": 5, "ip_address": "127.0.0.1"}
{"timestamp": "2025-07-06T20:59:36.760061Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:36.760844Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "SELECT users.id, users.username, users.email, users.hashed_password, users.avatar_url, users.is_online, users.is_active, users.created_at, users.updated_at, users.last_seen \nFROM users \nWHERE users.username = ?"}
{"timestamp": "2025-07-06T20:59:36.761126Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 65.86s ago] ('هادي',)"}
{"timestamp": "2025-07-06T20:59:36.762634Z", "level": "INFO", "logger": "chat.user_actions", "message": "User action: WebSocketConnected", "event": "WebSocketConnected", "user": "هادي", "user_id": 5}
{"timestamp": "2025-07-06T20:59:36.763799Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T20:59:36.764114Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[generated in 0.00040s] ('2025-07-06 20:59:36.763612', '2025-07-06 20:59:36.762776', 5)"}
{"timestamp": "2025-07-06T20:59:36.766805Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:59:36.781404Z", "level": "INFO", "logger": "chat.user_actions", "message": "User action: UserStatusOnline", "event": "UserStatusOnline", "user": "هادي", "user_id": 5}
{"timestamp": "2025-07-06T20:59:55.507584Z", "level": "WARNING", "logger": "root", "message": "Unknown message type: direct_message"}
{"timestamp": "2025-07-06T20:59:59.941264Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "BEGIN (implicit)"}
{"timestamp": "2025-07-06T20:59:59.941861Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "UPDATE users SET is_online=?, updated_at=?, last_seen=? WHERE users.id = ?"}
{"timestamp": "2025-07-06T20:59:59.942367Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "[cached since 23.23s ago] (0, '2025-07-06 20:59:59.941596', '2025-07-06 20:59:59.939783', 5)"}
{"timestamp": "2025-07-06T20:59:59.943941Z", "level": "INFO", "logger": "sqlalchemy.engine.Engine", "message": "COMMIT"}
{"timestamp": "2025-07-06T20:59:59.972694Z", "level": "INFO", "logger": "chat.user_actions", "message": "User action: UserStatusOffline", "event": "UserStatusOffline", "user": "هادي", "user_id": 5}
{"timestamp": "2025-07-06T20:59:59.972886Z", "level": "INFO", "logger": "chat.user_actions", "message": "User action: WebSocketDisconnected", "event": "WebSocketDisconnected", "user": "user_5", "user_id": 5}
{"timestamp": "2025-07-06T20:59:59.973076Z", "level": "INFO", "logger": "chat.user_actions", "message": "User action: WebSocketDisconnected", "event": "WebSocketDisconnected", "user": "هادي", "user_id": 5}
