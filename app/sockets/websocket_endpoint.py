"""
WebSocket endpoint
"""
import json
import logging
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_async_session
from app.models.user import User
from app.auth.jwt_handler import verify_token
from app.services.user_service import UserService
from app.sockets.connection_manager import manager
from app.sockets.websocket_handler import WebSocketHandler
from app.utils.logger import log_user_action, log_error


async def get_user_from_token(token: str, db: AsyncSession) -> User:
    """Get user from WebSocket token"""
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token is required"
        )
    
    # Verify token
    token_data = verify_token(token)
    if not token_data or not token_data.username:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
    
    # Get user
    user = await UserService.get_user_by_username(token_data.username, db)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    return user


async def websocket_endpoint(
    websocket: WebSocket,
    token: str,
    db: AsyncSession = Depends(get_async_session)
):
    """WebSocket endpoint for real-time communication"""
    user = None
    
    try:
        # Authenticate user
        user = await get_user_from_token(token, db)
        
        # Connect user
        await manager.connect(websocket, user)
        
        # Update user online status
        await UserService.update_user_status(user, True, db)
        
        # Listen for messages
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                
                # Parse JSON
                try:
                    message_data = json.loads(data)
                except json.JSONDecodeError:
                    logging.warning(f"Invalid JSON received from user {user.id}: {data}")
                    continue
                
                # Handle message
                await WebSocketHandler.handle_message(message_data, user, db)
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                log_error("Error in WebSocket loop", e, user_id=user.id if user else None)
                break
    
    except HTTPException as e:
        # Authentication failed
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    except Exception as e:
        log_error("Error in WebSocket endpoint", e, user_id=user.id if user else None)
    
    finally:
        # Cleanup on disconnect
        if user:
            # Update user offline status
            try:
                await UserService.update_user_status(user, False, db)
            except Exception as e:
                log_error("Error updating user status on disconnect", e, user_id=user.id)
            
            # Disconnect from manager
            manager.disconnect(user.id)
            
            log_user_action("WebSocketDisconnected", user_id=user.id, user=user.username)
