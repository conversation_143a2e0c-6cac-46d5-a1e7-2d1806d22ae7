"""
WebSocket connection manager
"""
import asyncio
import json
import logging
from typing import Dict, List, Set
from fastapi import WebSocket

from app.models.user import User
from app.utils.logger import log_user_action, log_error


class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        # Active connections: {user_id: websocket}
        self.active_connections: Dict[int, WebSocket] = {}
        
        # Room connections: {room_id: {user_id, ...}}
        self.room_connections: Dict[int, Set[int]] = {}
        
        # User to rooms mapping: {user_id: {room_id, ...}}
        self.user_rooms: Dict[int, Set[int]] = {}
    
    async def connect(self, websocket: WebSocket, user: User):
        """Connect a user"""
        await websocket.accept()
        self.active_connections[user.id] = websocket
        
        log_user_action("WebSocketConnected", user_id=user.id, user=user.username)
        
        # Notify others that user is online
        await self.broadcast_user_status(user.id, True)
    
    def disconnect(self, user_id: int):
        """Disconnect a user"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        
        # Remove user from all rooms
        if user_id in self.user_rooms:
            for room_id in self.user_rooms[user_id].copy():
                self.leave_room(user_id, room_id)
            del self.user_rooms[user_id]
        
        log_user_action("WebSocketDisconnected", user_id=user_id)
        
        # Notify others that user is offline
        asyncio.create_task(self.broadcast_user_status(user_id, False))
    
    def join_room(self, user_id: int, room_id: int):
        """Add user to a room"""
        if room_id not in self.room_connections:
            self.room_connections[room_id] = set()
        
        self.room_connections[room_id].add(user_id)
        
        if user_id not in self.user_rooms:
            self.user_rooms[user_id] = set()
        
        self.user_rooms[user_id].add(room_id)
        
        log_user_action("WebSocketJoinedRoom", user_id=user_id, room_id=room_id)
    
    def leave_room(self, user_id: int, room_id: int):
        """Remove user from a room"""
        if room_id in self.room_connections:
            self.room_connections[room_id].discard(user_id)
            
            # Clean up empty rooms
            if not self.room_connections[room_id]:
                del self.room_connections[room_id]
        
        if user_id in self.user_rooms:
            self.user_rooms[user_id].discard(room_id)
    
    async def send_personal_message(self, message: dict, user_id: int):
        """Send message to a specific user"""
        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]
                await websocket.send_text(json.dumps(message))
                return True
            except Exception as e:
                log_error("Error sending personal message", e, user_id=user_id)
                # Remove broken connection
                self.disconnect(user_id)
                return False
        return False
    
    async def send_room_message(self, message: dict, room_id: int, exclude_user: int = None):
        """Send message to all users in a room"""
        if room_id not in self.room_connections:
            return
        
        users_to_notify = self.room_connections[room_id].copy()
        if exclude_user:
            users_to_notify.discard(exclude_user)
        
        disconnected_users = []
        
        for user_id in users_to_notify:
            if user_id in self.active_connections:
                try:
                    websocket = self.active_connections[user_id]
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    log_error("Error sending room message", e, user_id=user_id, room_id=room_id)
                    disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    async def broadcast_user_status(self, user_id: int, is_online: bool):
        """Broadcast user online/offline status to all connected users"""
        message = {
            "type": "user_status",
            "data": {
                "user_id": user_id,
                "is_online": is_online
            }
        }
        
        disconnected_users = []
        
        for connected_user_id, websocket in self.active_connections.items():
            if connected_user_id != user_id:  # Don't send to the user themselves
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    log_error("Error broadcasting user status", e, user_id=connected_user_id)
                    disconnected_users.append(connected_user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    def get_online_users(self) -> List[int]:
        """Get list of online user IDs"""
        return list(self.active_connections.keys())
    
    def get_room_users(self, room_id: int) -> List[int]:
        """Get list of user IDs in a room"""
        return list(self.room_connections.get(room_id, set()))
    
    def is_user_online(self, user_id: int) -> bool:
        """Check if user is online"""
        return user_id in self.active_connections
    
    def is_user_in_room(self, user_id: int, room_id: int) -> bool:
        """Check if user is in a room"""
        return (room_id in self.room_connections and 
                user_id in self.room_connections[room_id])


# Global connection manager instance
manager = ConnectionManager()
