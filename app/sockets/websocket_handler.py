"""
WebSocket message handlers
"""
import json
import logging
from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.message import MessageType
from app.models.schemas import ChatMessage
from app.services.message_service import MessageService
from app.services.room_service import RoomService
from app.services.user_service import UserService
from app.sockets.connection_manager import manager
from app.utils.logger import log_message_action, log_room_action, log_error


class WebSocketHandler:
    """Handle WebSocket messages"""
    
    @staticmethod
    async def handle_message(websocket_data: Dict[str, Any], user: User, db: AsyncSession):
        """Handle incoming WebSocket message"""
        try:
            message_type = websocket_data.get("type")
            data = websocket_data.get("data", {})
            
            if message_type == "chat_message":
                await WebSocketHandler.handle_chat_message(data, user, db)
            elif message_type == "join_room":
                await WebSocketHandler.handle_join_room(data, user, db)
            elif message_type == "leave_room":
                await WebSocketHandler.handle_leave_room(data, user, db)
            elif message_type == "typing":
                await WebSocketHandler.handle_typing(data, user)
            elif message_type == "message_read":
                await WebSocketHandler.handle_message_read(data, user, db)
            else:
                logging.warning(f"Unknown message type: {message_type}")
                
        except Exception as e:
            log_error("Error handling WebSocket message", e, user_id=user.id)
    
    @staticmethod
    async def handle_chat_message(data: Dict[str, Any], user: User, db: AsyncSession):
        """Handle chat message"""
        try:
            # Validate message data
            content = data.get("content", "").strip()
            if not content:
                return
            
            recipient_id = data.get("recipient_id")
            room_id = data.get("room_id")
            
            # Determine message type
            if room_id:
                message_type = MessageType.ROOM
            elif recipient_id:
                message_type = MessageType.DIRECT
            else:
                return
            
            # Create message in database
            message_data = ChatMessage(
                content=content,
                recipient_id=recipient_id,
                room_id=room_id,
                message_type=message_type
            )
            
            message = await MessageService.create_message(message_data, user, db)
            
            # Prepare WebSocket message
            ws_message = {
                "type": "new_message",
                "data": {
                    "id": message.id,
                    "content": message.content,
                    "message_type": message.message_type.value,
                    "sender_id": message.sender_id,
                    "sender_username": user.username,
                    "recipient_id": message.recipient_id,
                    "room_id": message.room_id,
                    "created_at": message.created_at.isoformat(),
                    "status": message.status.value
                }
            }
            
            # Send message to recipients
            if message_type == MessageType.DIRECT:
                # Send to recipient
                await manager.send_personal_message(ws_message, recipient_id)
                
                # Send confirmation to sender
                confirmation = ws_message.copy()
                confirmation["type"] = "message_sent"
                await manager.send_personal_message(confirmation, user.id)
                
            elif message_type == MessageType.ROOM:
                # Send to all room members except sender
                await manager.send_room_message(ws_message, room_id, exclude_user=user.id)
                
                # Send confirmation to sender
                confirmation = ws_message.copy()
                confirmation["type"] = "message_sent"
                await manager.send_personal_message(confirmation, user.id)
            
        except Exception as e:
            log_error("Error handling chat message", e, user_id=user.id)
    
    @staticmethod
    async def handle_join_room(data: Dict[str, Any], user: User, db: AsyncSession):
        """Handle join room request"""
        try:
            room_id = data.get("room_id")
            if not room_id:
                return
            
            # Join room in database
            await RoomService.join_room(room_id, user, db)
            
            # Join room in WebSocket manager
            manager.join_room(user.id, room_id)
            
            # Notify room members
            notification = {
                "type": "user_joined_room",
                "data": {
                    "user_id": user.id,
                    "username": user.username,
                    "room_id": room_id
                }
            }
            
            await manager.send_room_message(notification, room_id, exclude_user=user.id)
            
            # Send confirmation to user
            confirmation = {
                "type": "room_joined",
                "data": {
                    "room_id": room_id,
                    "message": "Successfully joined room"
                }
            }
            await manager.send_personal_message(confirmation, user.id)
            
        except Exception as e:
            log_error("Error handling join room", e, user_id=user.id, room_id=data.get("room_id"))
    
    @staticmethod
    async def handle_leave_room(data: Dict[str, Any], user: User, db: AsyncSession):
        """Handle leave room request"""
        try:
            room_id = data.get("room_id")
            if not room_id:
                return
            
            # Leave room in database
            await RoomService.leave_room(room_id, user, db)
            
            # Leave room in WebSocket manager
            manager.leave_room(user.id, room_id)
            
            # Notify room members
            notification = {
                "type": "user_left_room",
                "data": {
                    "user_id": user.id,
                    "username": user.username,
                    "room_id": room_id
                }
            }
            
            await manager.send_room_message(notification, room_id)
            
            # Send confirmation to user
            confirmation = {
                "type": "room_left",
                "data": {
                    "room_id": room_id,
                    "message": "Successfully left room"
                }
            }
            await manager.send_personal_message(confirmation, user.id)
            
        except Exception as e:
            log_error("Error handling leave room", e, user_id=user.id, room_id=data.get("room_id"))
    
    @staticmethod
    async def handle_typing(data: Dict[str, Any], user: User):
        """Handle typing indicator"""
        try:
            recipient_id = data.get("recipient_id")
            room_id = data.get("room_id")
            is_typing = data.get("is_typing", False)
            
            typing_message = {
                "type": "typing",
                "data": {
                    "user_id": user.id,
                    "username": user.username,
                    "is_typing": is_typing,
                    "recipient_id": recipient_id,
                    "room_id": room_id
                }
            }
            
            if room_id:
                # Send to room members
                await manager.send_room_message(typing_message, room_id, exclude_user=user.id)
            elif recipient_id:
                # Send to specific user
                await manager.send_personal_message(typing_message, recipient_id)
                
        except Exception as e:
            log_error("Error handling typing indicator", e, user_id=user.id)
    
    @staticmethod
    async def handle_message_read(data: Dict[str, Any], user: User, db: AsyncSession):
        """Handle message read status"""
        try:
            message_id = data.get("message_id")
            if not message_id:
                return
            
            # Update message status in database
            from app.models.message import MessageStatus
            await MessageService.update_message_status(
                message_id, MessageStatus.READ, user, db
            )
            
            # Notify sender
            # Note: In a real implementation, you'd get the sender ID from the message
            # and send them a notification that their message was read
            
        except Exception as e:
            log_error("Error handling message read", e, user_id=user.id, message_id=data.get("message_id"))
