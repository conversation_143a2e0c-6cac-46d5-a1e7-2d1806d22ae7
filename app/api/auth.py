"""
Authentication API endpoints
"""
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_async_session
from app.models.schemas import User<PERSON><PERSON>, UserLogin, UserResponse, Token
from app.services.auth_service import AuthService
from app.auth.dependencies import get_current_active_user
from app.models.user import User

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=UserResponse, status_code=201)
async def register(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
):
    """Register a new user"""
    ip_address = request.client.host if request.client else None
    user = await AuthService.register_user(user_data, db, ip_address)
    return UserResponse.from_orm(user)


@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_async_session)
):
    """Login user and get access token"""
    ip_address = request.client.host if request.client else None
    return await AuthService.authenticate_user(login_data, db, ip_address)


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_session)
):
    """Logout current user"""
    ip_address = request.client.host if request.client else None
    await AuthService.logout_user(current_user, db, ip_address)
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information"""
    return UserResponse.from_orm(current_user)
