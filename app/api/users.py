"""
Users API endpoints
"""
from typing import Optional
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_async_session
from app.models.schemas import UserResponse, UserUpdate, PaginationParams, PaginatedResponse
from app.services.user_service import UserService
from app.auth.dependencies import get_current_active_user
from app.models.user import User

router = APIRouter(prefix="/users", tags=["Users"])


@router.get("/", response_model=PaginatedResponse)
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None, description="Search by username"),
    online_only: bool = Query(False, description="Show only online users"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get users with pagination and optional filters"""
    pagination = PaginationParams(page=page, size=size)
    users, total = await UserService.get_users(pagination, db, search, online_only)
    
    # Convert users to response format
    user_items = [UserResponse.from_orm(user).dict() for user in users]
    
    pages = (total + size - 1) // size  # Calculate total pages
    
    return PaginatedResponse(
        items=user_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/online", response_model=list[UserResponse])
async def get_online_users(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get all online users"""
    users = await UserService.get_online_users(db)
    return [UserResponse.from_orm(user) for user in users]


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get user by ID"""
    user = await UserService.get_user_by_id(user_id, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return UserResponse.from_orm(user)


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Update user information"""
    user = await UserService.update_user(user_id, user_data, db, current_user)
    return UserResponse.from_orm(user)


@router.get("/username/{username}", response_model=UserResponse)
async def get_user_by_username(
    username: str,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get user by username"""
    user = await UserService.get_user_by_username(username, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return UserResponse.from_orm(user)
