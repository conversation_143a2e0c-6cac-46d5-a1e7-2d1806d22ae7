"""
Messages API endpoints
"""
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_async_session
from app.models.schemas import (
    MessageCreate, MessageResponse, MessageUpdate, 
    PaginationParams, PaginatedResponse
)
from app.models.message import MessageStatus
from app.services.message_service import MessageService
from app.auth import get_current_active_user
from app.models.user import User

router = APIRouter(prefix="/messages", tags=["Messages"])


@router.post("/", response_model=MessageResponse, status_code=201)
async def create_message(
    message_data: MessageCreate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new message"""
    message = await MessageService.create_message(message_data, current_user, db)
    return MessageResponse.from_orm(message)


@router.get("/direct/{user_id}", response_model=PaginatedResponse)
async def get_direct_messages(
    user_id: int = Path(..., description="ID of the other user"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get direct messages between current user and another user"""
    pagination = PaginationParams(page=page, size=size)
    messages, total = await MessageService.get_direct_messages(
        current_user.id, user_id, pagination, db
    )
    
    # Convert messages to response format
    message_items = [MessageResponse.from_orm(message).dict() for message in messages]
    
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=message_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/room/{room_id}", response_model=PaginatedResponse)
async def get_room_messages(
    room_id: int = Path(..., description="ID of the room"),
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get messages from a room"""
    pagination = PaginationParams(page=page, size=size)
    messages, total = await MessageService.get_room_messages(
        room_id, pagination, current_user, db
    )
    
    # Convert messages to response format
    message_items = [MessageResponse.from_orm(message).dict() for message in messages]
    
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=message_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.patch("/{message_id}/status", response_model=MessageResponse)
async def update_message_status(
    message_id: int = Path(..., description="ID of the message"),
    status: MessageStatus = Query(..., description="New message status"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Update message status (delivered, read)"""
    message = await MessageService.update_message_status(
        message_id, status, current_user, db
    )
    return MessageResponse.from_orm(message)
