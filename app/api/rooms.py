"""
Rooms API endpoints
"""
from typing import Optional
from fastapi import APIRouter, Depends, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.database import get_async_session
from app.models.schemas import (
    RoomCreate, RoomResponse, RoomMemberResponse,
    PaginationParams, PaginatedResponse
)
from app.models.room import RoomType
from app.services.room_service import RoomService
from app.auth import get_current_active_user
from app.models.user import User

router = APIRouter(prefix="/rooms", tags=["Rooms"])


@router.post("/", response_model=RoomResponse, status_code=201)
async def create_room(
    room_data: RoomCreate,
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new room"""
    room = await RoomService.create_room(room_data, current_user, db)
    return RoomResponse.from_orm(room)


@router.get("/", response_model=PaginatedResponse)
async def get_rooms(
    page: int = Query(1, ge=1),
    size: int = Query(50, ge=1, le=100),
    search: Optional[str] = Query(None, description="Search by room name"),
    room_type: Optional[RoomType] = Query(None, description="Filter by room type"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get rooms with pagination and optional filters"""
    pagination = PaginationParams(page=page, size=size)
    rooms, total = await RoomService.get_rooms(pagination, db, search, room_type)
    
    # Convert rooms to response format
    room_items = [RoomResponse.from_orm(room).dict() for room in rooms]
    
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=room_items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/my-rooms", response_model=list[RoomResponse])
async def get_my_rooms(
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get rooms that current user is a member of"""
    rooms = await RoomService.get_user_rooms(current_user, db)
    return [RoomResponse.from_orm(room) for room in rooms]


@router.get("/{room_id}", response_model=RoomResponse)
async def get_room(
    room_id: int = Path(..., description="ID of the room"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get room by ID"""
    room = await RoomService.get_room_by_id(room_id, db)
    if not room:
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Room not found"
        )
    return RoomResponse.from_orm(room)


@router.post("/{room_id}/join", response_model=RoomMemberResponse)
async def join_room(
    room_id: int = Path(..., description="ID of the room to join"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Join a room"""
    membership = await RoomService.join_room(room_id, current_user, db)
    return RoomMemberResponse.from_orm(membership)


@router.post("/{room_id}/leave")
async def leave_room(
    room_id: int = Path(..., description="ID of the room to leave"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Leave a room"""
    await RoomService.leave_room(room_id, current_user, db)
    return {"message": "Successfully left the room"}


@router.get("/{room_id}/members", response_model=list[RoomMemberResponse])
async def get_room_members(
    room_id: int = Path(..., description="ID of the room"),
    db: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(get_current_active_user)
):
    """Get room members"""
    members = await RoomService.get_room_members(room_id, current_user, db)
    return [RoomMemberResponse.from_orm(member) for member in members]
