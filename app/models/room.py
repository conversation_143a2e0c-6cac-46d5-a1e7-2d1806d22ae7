"""
Room and RoomMember models for the chat application
"""
from datetime import datetime
from enum import Enum
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, ForeignKey, Boolean, Enum as SQLEnum
from sqlalchemy.orm import relationship

from app.models.database import Base


class RoomType(str, Enum):
    """Room type enumeration"""
    PUBLIC = "public"
    PRIVATE = "private"


class MemberRole(str, Enum):
    """Member role enumeration"""
    ADMIN = "admin"
    MODERATOR = "moderator"
    MEMBER = "member"


class Room(Base):
    """Room model"""
    __tablename__ = "rooms"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    room_type = Column(SQLEnum(RoomType), nullable=False, default=RoomType.PUBLIC)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign key
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    creator = relationship("User")
    messages = relationship("Message", back_populates="room")
    members = relationship("RoomMember", back_populates="room")
    
    def __repr__(self):
        return f"<Room(id={self.id}, name='{self.name}', type='{self.room_type}')>"
    
    def to_dict(self):
        """Convert room to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "room_type": self.room_type.value,
            "is_active": self.is_active,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "member_count": len(self.members) if self.members else 0
        }


class RoomMember(Base):
    """Room membership model"""
    __tablename__ = "room_members"
    
    id = Column(Integer, primary_key=True, index=True)
    role = Column(SQLEnum(MemberRole), nullable=False, default=MemberRole.MEMBER)
    joined_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="room_memberships")
    room = relationship("Room", back_populates="members")
    
    def __repr__(self):
        return f"<RoomMember(user_id={self.user_id}, room_id={self.room_id}, role='{self.role}')>"
    
    def to_dict(self):
        """Convert room member to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "room_id": self.room_id,
            "role": self.role.value,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
            "is_active": self.is_active
        }
