"""
Database configuration and setup
"""
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from config import settings

# Create the SQLAlchemy engine
# Convert postgresql:// to postgresql+asyncpg:// for async support
async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")

# Async engine for async operations
async_engine = create_async_engine(
    async_database_url,
    echo=settings.debug,
    future=True
)

# Async session maker
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Sync engine for migrations and initial setup
sync_engine = create_engine(
    settings.database_url,
    echo=settings.debug
)

# Sync session maker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

# Base class for all models
Base = declarative_base()


async def get_async_session() -> AsyncSession:
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def get_sync_session():
    """Get sync database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def create_tables():
    """Create all database tables"""
    try:
        async with async_engine.begin() as conn:
            # Import all models to ensure they are registered
            from app.models.user import User
            from app.models.message import Message
            from app.models.room import Room, RoomMember
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logging.info("Database tables created successfully")
    except Exception as e:
        logging.error(f"Error creating database tables: {e}")
        raise
