"""
Message model for the chat application
"""
from datetime import datetime
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship

from app.models.database import Base


class MessageStatus(str, Enum):
    """Message status enumeration"""
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"


class MessageType(str, Enum):
    """Message type enumeration"""
    DIRECT = "direct"  # One-to-one message
    ROOM = "room"      # Room message


class Message(Base):
    """Message model"""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    message_type = Column(SQLEnum(MessageType), nullable=False, default=MessageType.DIRECT)
    status = Column(SQLEnum(MessageStatus), nullable=False, default=MessageStatus.SENT)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    recipient_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Null for room messages
    room_id = Column(Integer, ForeignKey("rooms.id"), nullable=True)  # Null for direct messages
    
    # Relationships
    sender = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    recipient = relationship("User", foreign_keys=[recipient_id], back_populates="received_messages")
    room = relationship("Room", back_populates="messages")
    
    def __repr__(self):
        return f"<Message(id={self.id}, sender_id={self.sender_id}, type='{self.message_type}')>"
    
    def to_dict(self):
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "content": self.content,
            "message_type": self.message_type.value,
            "status": self.status.value,
            "sender_id": self.sender_id,
            "recipient_id": self.recipient_id,
            "room_id": self.room_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
