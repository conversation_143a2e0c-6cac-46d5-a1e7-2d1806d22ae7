"""
Logging configuration and utilities
"""
import json
import logging
import os
from datetime import datetime
from typing import Dict, Any

from config import settings


class J<PERSON>NFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }
        
        # Add extra fields if present
        if hasattr(record, 'event'):
            log_entry['event'] = record.event
        if hasattr(record, 'user'):
            log_entry['user'] = record.user
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'recipient'):
            log_entry['recipient'] = record.recipient
        if hasattr(record, 'recipient_id'):
            log_entry['recipient_id'] = record.recipient_id
        if hasattr(record, 'room_id'):
            log_entry['room_id'] = record.room_id
        if hasattr(record, 'message_id'):
            log_entry['message_id'] = record.message_id
        if hasattr(record, 'ip_address'):
            log_entry['ip_address'] = record.ip_address
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logging():
    """Setup logging configuration"""
    # Ensure logs directory exists
    os.makedirs("logs", exist_ok=True)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with simple format
    console_handler = logging.StreamHandler()
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)
    
    # File handler with JSON format
    file_handler = logging.FileHandler(settings.log_file, encoding='utf-8')
    file_handler.setFormatter(JSONFormatter())
    file_handler.setLevel(logging.DEBUG)
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Set specific loggers
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)


def log_user_action(event: str, user_id: int, user: str = None, **kwargs):
    """Log user actions with structured data"""
    logger = logging.getLogger("chat.user_actions")
    extra = {
        'event': event,
        'user_id': user_id,
        'user': user or f"user_{user_id}",
        **kwargs
    }
    logger.info(f"User action: {event}", extra=extra)


def log_message_action(event: str, message_id: str, sender_id: int, 
                      recipient_id: int = None, room_id: int = None, **kwargs):
    """Log message actions with structured data"""
    logger = logging.getLogger("chat.messages")
    extra = {
        'event': event,
        'message_id': message_id,
        'user_id': sender_id,
        **kwargs
    }
    
    if recipient_id:
        extra['recipient_id'] = recipient_id
    if room_id:
        extra['room_id'] = room_id
    
    logger.info(f"Message action: {event}", extra=extra)


def log_room_action(event: str, room_id: int, user_id: int, **kwargs):
    """Log room actions with structured data"""
    logger = logging.getLogger("chat.rooms")
    extra = {
        'event': event,
        'room_id': room_id,
        'user_id': user_id,
        **kwargs
    }
    logger.info(f"Room action: {event}", extra=extra)


def log_auth_action(event: str, user_id: int = None, username: str = None, 
                   ip_address: str = None, **kwargs):
    """Log authentication actions with structured data"""
    logger = logging.getLogger("chat.auth")
    extra = {
        'event': event,
        **kwargs
    }
    
    if user_id:
        extra['user_id'] = user_id
    if username:
        extra['user'] = username
    if ip_address:
        extra['ip_address'] = ip_address
    
    logger.info(f"Auth action: {event}", extra=extra)


def log_error(message: str, error: Exception = None, **kwargs):
    """Log errors with structured data"""
    logger = logging.getLogger("chat.errors")
    extra = kwargs
    
    if error:
        logger.error(f"{message}: {str(error)}", exc_info=error, extra=extra)
    else:
        logger.error(message, extra=extra)
