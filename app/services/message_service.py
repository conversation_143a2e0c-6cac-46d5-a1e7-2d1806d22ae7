"""
Message service
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, and_
from fastapi import HTTPException, status

from app.models.message import Message, MessageType, MessageStatus
from app.models.user import User
from app.models.room import Room, RoomMember
from app.models.schemas import MessageCreate, MessageUpdate, PaginationParams
from app.utils.logger import log_message_action, log_error


class MessageService:
    """Message service class"""
    
    @staticmethod
    async def create_message(
        message_data: MessageCreate,
        sender: User,
        db: AsyncSession
    ) -> Message:
        """Create a new message"""
        try:
            # Validate message type and recipients
            if message_data.message_type == MessageType.DIRECT:
                if not message_data.recipient_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Recipient ID is required for direct messages"
                    )
                
                # Check if recipient exists
                stmt = select(User).where(User.id == message_data.recipient_id)
                result = await db.execute(stmt)
                recipient = result.scalar_one_or_none()
                if not recipient:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Recipient not found"
                    )
                
                # Check if sender is not sending to themselves
                if sender.id == message_data.recipient_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Cannot send message to yourself"
                    )
                
            elif message_data.message_type == MessageType.ROOM:
                if not message_data.room_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Room ID is required for room messages"
                    )
                
                # Check if room exists and user is a member
                stmt = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == message_data.room_id,
                        RoomMember.user_id == sender.id,
                        RoomMember.is_active == True
                    )
                )
                result = await db.execute(stmt)
                membership = result.scalar_one_or_none()
                if not membership:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You are not a member of this room"
                    )
            
            # Create message
            message = Message(
                content=message_data.content,
                message_type=message_data.message_type,
                sender_id=sender.id,
                recipient_id=message_data.recipient_id,
                room_id=message_data.room_id
            )
            
            db.add(message)
            await db.commit()
            await db.refresh(message)
            
            # Log message creation
            log_message_action(
                "MessageSent",
                message_id=f"msg_{message.id}",
                sender_id=sender.id,
                recipient_id=message_data.recipient_id,
                room_id=message_data.room_id
            )
            
            return message
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error creating message", e, user_id=sender.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create message"
            )
    
    @staticmethod
    async def get_direct_messages(
        user1_id: int,
        user2_id: int,
        pagination: PaginationParams,
        db: AsyncSession
    ) -> tuple[List[Message], int]:
        """Get direct messages between two users"""
        try:
            # Base query for messages between two users
            base_filter = and_(
                Message.message_type == MessageType.DIRECT,
                or_(
                    and_(Message.sender_id == user1_id, Message.recipient_id == user2_id),
                    and_(Message.sender_id == user2_id, Message.recipient_id == user1_id)
                )
            )
            
            # Count query
            count_stmt = select(func.count(Message.id)).where(base_filter)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Messages query with pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = select(Message).where(base_filter)
            stmt = stmt.order_by(Message.created_at.desc())
            stmt = stmt.offset(offset).limit(pagination.size)
            
            result = await db.execute(stmt)
            messages = list(result.scalars().all())
            
            return messages, total
            
        except Exception as e:
            log_error("Error getting direct messages", e, user_id=user1_id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get messages"
            )
    
    @staticmethod
    async def get_room_messages(
        room_id: int,
        pagination: PaginationParams,
        user: User,
        db: AsyncSession
    ) -> tuple[List[Message], int]:
        """Get messages from a room"""
        try:
            # Check if user is a member of the room
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == user.id,
                    RoomMember.is_active == True
                )
            )
            result = await db.execute(stmt)
            membership = result.scalar_one_or_none()
            if not membership:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You are not a member of this room"
                )
            
            # Base query for room messages
            base_filter = and_(
                Message.message_type == MessageType.ROOM,
                Message.room_id == room_id
            )
            
            # Count query
            count_stmt = select(func.count(Message.id)).where(base_filter)
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Messages query with pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = select(Message).where(base_filter)
            stmt = stmt.order_by(Message.created_at.desc())
            stmt = stmt.offset(offset).limit(pagination.size)
            
            result = await db.execute(stmt)
            messages = list(result.scalars().all())
            
            return messages, total
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error getting room messages", e, room_id=room_id, user_id=user.id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get room messages"
            )
    
    @staticmethod
    async def update_message_status(
        message_id: int,
        new_status: MessageStatus,
        user: User,
        db: AsyncSession
    ) -> Message:
        """Update message status (delivered, read)"""
        try:
            # Get message
            stmt = select(Message).where(Message.id == message_id)
            result = await db.execute(stmt)
            message = result.scalar_one_or_none()
            
            if not message:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Message not found"
                )
            
            # Check if user is the recipient (for direct messages) or member (for room messages)
            if message.message_type == MessageType.DIRECT:
                if message.recipient_id != user.id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You can only update status of messages sent to you"
                    )
            elif message.message_type == MessageType.ROOM:
                # Check room membership
                stmt = select(RoomMember).where(
                    and_(
                        RoomMember.room_id == message.room_id,
                        RoomMember.user_id == user.id,
                        RoomMember.is_active == True
                    )
                )
                result = await db.execute(stmt)
                membership = result.scalar_one_or_none()
                if not membership:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="You are not a member of this room"
                    )
            
            # Update status
            message.status = new_status
            await db.commit()
            await db.refresh(message)
            
            log_message_action(
                f"MessageStatus{new_status.value.title()}",
                message_id=f"msg_{message.id}",
                sender_id=message.sender_id,
                recipient_id=message.recipient_id,
                room_id=message.room_id
            )
            
            return message
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error updating message status", e, message_id=message_id, user_id=user.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update message status"
            )
