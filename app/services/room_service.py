"""
Room service
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from fastapi import HTTPException, status

from app.models.room import Room, RoomMember, RoomType, MemberRole
from app.models.user import User
from app.models.schemas import Room<PERSON><PERSON>, RoomUpdate, PaginationParams
from app.utils.logger import log_room_action, log_error


class RoomService:
    """Room service class"""
    
    @staticmethod
    async def create_room(
        room_data: RoomCreate,
        creator: User,
        db: AsyncSession
    ) -> Room:
        """Create a new room"""
        try:
            # Check if room name already exists
            stmt = select(Room).where(Room.name == room_data.name)
            result = await db.execute(stmt)
            if result.scalar_one_or_none():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Room name already exists"
                )
            
            # Create room
            room = Room(
                name=room_data.name,
                description=room_data.description,
                room_type=room_data.room_type,
                created_by=creator.id
            )
            
            db.add(room)
            await db.flush()  # Get room ID
            
            # Add creator as admin member
            membership = RoomMember(
                user_id=creator.id,
                room_id=room.id,
                role=MemberRole.ADMIN
            )
            
            db.add(membership)
            await db.commit()
            await db.refresh(room)
            
            log_room_action("RoomCreated", room_id=room.id, user_id=creator.id)
            
            return room
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error creating room", e, user_id=creator.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create room"
            )
    
    @staticmethod
    async def get_room_by_id(room_id: int, db: AsyncSession) -> Optional[Room]:
        """Get room by ID"""
        try:
            stmt = select(Room).where(Room.id == room_id, Room.is_active == True)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            log_error("Error getting room by ID", e, room_id=room_id)
            return None
    
    @staticmethod
    async def get_rooms(
        pagination: PaginationParams,
        db: AsyncSession,
        search: Optional[str] = None,
        room_type: Optional[RoomType] = None
    ) -> tuple[List[Room], int]:
        """Get rooms with pagination and optional filters"""
        try:
            # Base query
            stmt = select(Room).where(Room.is_active == True)
            count_stmt = select(func.count(Room.id)).where(Room.is_active == True)
            
            # Apply filters
            if search:
                search_filter = f"%{search}%"
                stmt = stmt.where(Room.name.ilike(search_filter))
                count_stmt = count_stmt.where(Room.name.ilike(search_filter))
            
            if room_type:
                stmt = stmt.where(Room.room_type == room_type)
                count_stmt = count_stmt.where(Room.room_type == room_type)
            
            # Get total count
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = stmt.offset(offset).limit(pagination.size)
            stmt = stmt.order_by(Room.created_at.desc())
            
            # Execute query
            result = await db.execute(stmt)
            rooms = result.scalars().all()
            
            return list(rooms), total
            
        except Exception as e:
            log_error("Error getting rooms", e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get rooms"
            )
    
    @staticmethod
    async def join_room(room_id: int, user: User, db: AsyncSession) -> RoomMember:
        """Join a room"""
        try:
            # Check if room exists
            room = await RoomService.get_room_by_id(room_id, db)
            if not room:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Room not found"
                )
            
            # Check if user is already a member
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == user.id
                )
            )
            result = await db.execute(stmt)
            existing_membership = result.scalar_one_or_none()
            
            if existing_membership:
                if existing_membership.is_active:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="User is already a member of this room"
                    )
                else:
                    # Reactivate membership
                    existing_membership.is_active = True
                    await db.commit()
                    await db.refresh(existing_membership)
                    
                    log_room_action("RoomRejoined", room_id=room_id, user_id=user.id)
                    return existing_membership
            
            # Create new membership
            membership = RoomMember(
                user_id=user.id,
                room_id=room_id,
                role=MemberRole.MEMBER
            )
            
            db.add(membership)
            await db.commit()
            await db.refresh(membership)
            
            log_room_action("RoomJoined", room_id=room_id, user_id=user.id)
            
            return membership
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error joining room", e, room_id=room_id, user_id=user.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to join room"
            )
    
    @staticmethod
    async def leave_room(room_id: int, user: User, db: AsyncSession):
        """Leave a room"""
        try:
            # Get membership
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == user.id,
                    RoomMember.is_active == True
                )
            )
            result = await db.execute(stmt)
            membership = result.scalar_one_or_none()
            
            if not membership:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="You are not a member of this room"
                )
            
            # Deactivate membership
            membership.is_active = False
            await db.commit()
            
            log_room_action("RoomLeft", room_id=room_id, user_id=user.id)
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error leaving room", e, room_id=room_id, user_id=user.id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to leave room"
            )
    
    @staticmethod
    async def get_room_members(room_id: int, user: User, db: AsyncSession) -> List[RoomMember]:
        """Get room members"""
        try:
            # Check if user is a member of the room
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.user_id == user.id,
                    RoomMember.is_active == True
                )
            )
            result = await db.execute(stmt)
            user_membership = result.scalar_one_or_none()
            
            if not user_membership:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You are not a member of this room"
                )
            
            # Get all active members
            stmt = select(RoomMember).where(
                and_(
                    RoomMember.room_id == room_id,
                    RoomMember.is_active == True
                )
            )
            result = await db.execute(stmt)
            members = result.scalars().all()
            
            return list(members)
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error getting room members", e, room_id=room_id, user_id=user.id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get room members"
            )
    
    @staticmethod
    async def get_user_rooms(user: User, db: AsyncSession) -> List[Room]:
        """Get rooms that user is a member of"""
        try:
            stmt = select(Room).join(RoomMember).where(
                and_(
                    RoomMember.user_id == user.id,
                    RoomMember.is_active == True,
                    Room.is_active == True
                )
            ).order_by(Room.name)
            
            result = await db.execute(stmt)
            rooms = result.scalars().all()
            
            return list(rooms)
            
        except Exception as e:
            log_error("Error getting user rooms", e, user_id=user.id)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get user rooms"
            )
