"""
Authentication service
"""
import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import HTT<PERSON>Ex<PERSON>, status

from app.models.user import User
from app.models.schemas import User<PERSON><PERSON>, User<PERSON>ogin, Token
from app.auth import hash_password, verify_password, is_password_strong, create_access_token
from app.utils.logger import log_auth_action, log_error


class AuthService:
    """Authentication service class"""
    
    @staticmethod
    async def register_user(user_data: UserCreate, db: AsyncSession, ip_address: str = None) -> User:
        """Register a new user"""
        try:
            # Check if username already exists
            stmt = select(User).where(User.username == user_data.username)
            result = await db.execute(stmt)
            if result.scalar_one_or_none():
                log_auth_action("RegistrationFailed", username=user_data.username, 
                              ip_address=ip_address, reason="Username already exists")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already registered"
                )
            
            # Check if email already exists
            stmt = select(User).where(User.email == user_data.email)
            result = await db.execute(stmt)
            if result.scalar_one_or_none():
                log_auth_action("RegistrationFailed", username=user_data.username, 
                              ip_address=ip_address, reason="Email already exists")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            
            # Validate password strength
            is_strong, message = is_password_strong(user_data.password)
            if not is_strong:
                log_auth_action("RegistrationFailed", username=user_data.username, 
                              ip_address=ip_address, reason=f"Weak password: {message}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=message
                )
            
            # Hash password
            hashed_password = hash_password(user_data.password)
            
            # Create user
            db_user = User(
                username=user_data.username,
                email=user_data.email,
                hashed_password=hashed_password
            )
            
            db.add(db_user)
            await db.commit()
            await db.refresh(db_user)
            
            log_auth_action("UserRegistered", user_id=db_user.id, 
                          username=db_user.username, ip_address=ip_address)
            
            return db_user
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Registration error", e, username=user_data.username, ip_address=ip_address)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed"
            )
    
    @staticmethod
    async def authenticate_user(login_data: UserLogin, db: AsyncSession, ip_address: str = None) -> Token:
        """Authenticate user and return token"""
        try:
            # Get user by username
            stmt = select(User).where(User.username == login_data.username)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user:
                log_auth_action("LoginFailed", username=login_data.username, 
                              ip_address=ip_address, reason="User not found")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect username or password"
                )
            
            # Verify password
            if not verify_password(login_data.password, user.hashed_password):
                log_auth_action("LoginFailed", user_id=user.id, username=user.username, 
                              ip_address=ip_address, reason="Invalid password")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect username or password"
                )
            
            # Check if user is active
            if not user.is_active:
                log_auth_action("LoginFailed", user_id=user.id, username=user.username, 
                              ip_address=ip_address, reason="User inactive")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Inactive user"
                )
            
            # Update last seen and online status
            user.last_seen = datetime.utcnow()
            user.is_online = True
            await db.commit()
            
            # Create access token
            access_token = create_access_token(data={"sub": user.username})
            
            log_auth_action("UserLogin", user_id=user.id, username=user.username, 
                          ip_address=ip_address)
            
            return Token(access_token=access_token, token_type="bearer")
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Login error", e, username=login_data.username, ip_address=ip_address)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Login failed"
            )
    
    @staticmethod
    async def logout_user(user: User, db: AsyncSession, ip_address: str = None):
        """Logout user (update online status)"""
        try:
            user.is_online = False
            user.last_seen = datetime.utcnow()
            await db.commit()
            
            log_auth_action("UserLogout", user_id=user.id, username=user.username, 
                          ip_address=ip_address)
            
        except Exception as e:
            log_error("Logout error", e, user_id=user.id, ip_address=ip_address)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed"
            )
