"""
User service
"""
from datetime import datetime
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from fastapi import HTTPException, status

from app.models.user import User
from app.models.schemas import UserUpdate, PaginationParams
from app.utils.logger import log_user_action, log_error


class UserService:
    """User service class"""
    
    @staticmethod
    async def get_user_by_id(user_id: int, db: AsyncSession) -> Optional[User]:
        """Get user by ID"""
        try:
            stmt = select(User).where(User.id == user_id)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            log_error("Error getting user by ID", e, user_id=user_id)
            return None
    
    @staticmethod
    async def get_user_by_username(username: str, db: AsyncSession) -> Optional[User]:
        """Get user by username"""
        try:
            stmt = select(User).where(User.username == username)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            log_error("Error getting user by username", e, username=username)
            return None
    
    @staticmethod
    async def get_users(
        pagination: PaginationParams,
        db: AsyncSession,
        search: Optional[str] = None,
        online_only: bool = False
    ) -> tuple[List[User], int]:
        """Get users with pagination and optional filters"""
        try:
            # Base query
            stmt = select(User).where(User.is_active == True)
            count_stmt = select(func.count(User.id)).where(User.is_active == True)
            
            # Apply filters
            if search:
                search_filter = f"%{search}%"
                stmt = stmt.where(User.username.ilike(search_filter))
                count_stmt = count_stmt.where(User.username.ilike(search_filter))
            
            if online_only:
                stmt = stmt.where(User.is_online == True)
                count_stmt = count_stmt.where(User.is_online == True)
            
            # Get total count
            count_result = await db.execute(count_stmt)
            total = count_result.scalar()
            
            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            stmt = stmt.offset(offset).limit(pagination.size)
            stmt = stmt.order_by(User.username)
            
            # Execute query
            result = await db.execute(stmt)
            users = result.scalars().all()
            
            return list(users), total
            
        except Exception as e:
            log_error("Error getting users", e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get users"
            )
    
    @staticmethod
    async def update_user(
        user_id: int,
        user_data: UserUpdate,
        db: AsyncSession,
        current_user: User
    ) -> User:
        """Update user information"""
        try:
            # Check if user exists
            user = await UserService.get_user_by_id(user_id, db)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )
            
            # Check permissions (users can only update their own profile)
            if user.id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not enough permissions"
                )
            
            # Update fields
            update_data = user_data.dict(exclude_unset=True)
            
            # Check if username is being changed and if it's available
            if "username" in update_data and update_data["username"] != user.username:
                existing_user = await UserService.get_user_by_username(update_data["username"], db)
                if existing_user:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Username already taken"
                    )
            
            # Check if email is being changed and if it's available
            if "email" in update_data and update_data["email"] != user.email:
                stmt = select(User).where(User.email == update_data["email"])
                result = await db.execute(stmt)
                existing_user = result.scalar_one_or_none()
                if existing_user:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already taken"
                    )
            
            # Apply updates
            for field, value in update_data.items():
                setattr(user, field, value)
            
            user.updated_at = datetime.utcnow()
            await db.commit()
            await db.refresh(user)
            
            log_user_action("UserUpdated", user_id=user.id, user=user.username)
            
            return user
            
        except HTTPException:
            raise
        except Exception as e:
            log_error("Error updating user", e, user_id=user_id)
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update user"
            )
    
    @staticmethod
    async def update_user_status(user: User, is_online: bool, db: AsyncSession):
        """Update user online status"""
        try:
            user.is_online = is_online
            user.last_seen = datetime.utcnow()
            await db.commit()
            
            status_text = "online" if is_online else "offline"
            log_user_action(f"UserStatus{status_text.title()}", user_id=user.id, user=user.username)
            
        except Exception as e:
            log_error("Error updating user status", e, user_id=user.id)
            await db.rollback()
    
    @staticmethod
    async def get_online_users(db: AsyncSession) -> List[User]:
        """Get all online users"""
        try:
            stmt = select(User).where(User.is_online == True, User.is_active == True)
            result = await db.execute(stmt)
            return list(result.scalars().all())
        except Exception as e:
            log_error("Error getting online users", e)
            return []
