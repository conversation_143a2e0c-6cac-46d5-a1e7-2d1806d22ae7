# 🌐 Frontend للدردشة الفورية

## 📋 الوصف
واجهة ويب بسيطة مكتوبة بـ HTML/CSS/JavaScript للتفاعل مع API الدردشة.

## 🚀 كيفية التشغيل

### الطريقة 1: فتح الملف مباشرة
```bash
# افتح الملف في المتصفح
open frontend/index.html
# أو
firefox frontend/index.html
# أو
google-chrome frontend/index.html
```

### الطريقة 2: خادم HTTP بسيط
```bash
# باستخدام Python
cd frontend
python3 -m http.server 3000

# ثم افتح المتصفح على
# http://localhost:3000
```

## 🔧 المتطلبات
- Backend يعمل على `http://localhost:8000`
- متصفح حديث يدعم WebSocket

## 📱 المميزات

### ✅ تسجيل المستخدمين
- إنشاء حساب جديد
- تسجيل الدخول
- تسجيل الخروج

### ✅ الرسائل الفورية
- إرسال رسائل فورية
- استقبال رسائل من المستخدمين الآخرين
- عرض الرسائل في الوقت الفعلي

### ✅ واجهة سهلة الاستخدام
- تصميم عربي (RTL)
- رسائل حالة واضحة
- تفاعل سهل

## 🔗 كيف يعمل الربط مع Backend

### 1. HTTP Requests للمصادقة
```javascript
// تسجيل مستخدم جديد
fetch('http://localhost:8000/api/v1/auth/register', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
    })
})

// تسجيل الدخول
fetch('http://localhost:8000/api/v1/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        username: 'testuser',
        password: 'password123'
    })
})
```

### 2. WebSocket للرسائل الفورية
```javascript
// الاتصال بـ WebSocket
const websocket = new WebSocket(`ws://localhost:8000/ws?token=${authToken}`);

// استقبال رسائل
websocket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    displayMessage(data);
};

// إرسال رسالة
websocket.send(JSON.stringify({
    type: 'direct_message',
    content: 'مرحبا!',
    recipient_username: 'otheruser'
}));
```

## 🎯 للتطوير المتقدم

### استخدام React
```jsx
import React, { useState, useEffect } from 'react';

function ChatApp() {
    const [token, setToken] = useState(null);
    const [messages, setMessages] = useState([]);
    
    // تسجيل الدخول
    const login = async (username, password) => {
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        const data = await response.json();
        setToken(data.access_token);
    };
    
    // WebSocket
    useEffect(() => {
        if (token) {
            const ws = new WebSocket(`ws://localhost:8000/ws?token=${token}`);
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                setMessages(prev => [...prev, data]);
            };
        }
    }, [token]);
    
    return (
        <div>
            {/* واجهة المستخدم */}
        </div>
    );
}
```

### استخدام Vue.js
```vue
<template>
    <div>
        <div v-if="!token">
            <!-- نموذج تسجيل الدخول -->
        </div>
        <div v-else>
            <!-- واجهة الدردشة -->
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            token: null,
            messages: [],
            websocket: null
        }
    },
    methods: {
        async login(username, password) {
            // تسجيل الدخول
        },
        connectWebSocket() {
            // الاتصال بـ WebSocket
        }
    }
}
</script>
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:
1. **CORS Error**: تأكد من تشغيل Backend مع CORS enabled
2. **WebSocket Connection Failed**: تحقق من الـ token
3. **API Not Found**: تأكد من تشغيل Backend على المنفذ الصحيح

### حلول:
```javascript
// للتحقق من حالة الاتصال
console.log('WebSocket State:', websocket.readyState);

// للتحقق من الـ token
console.log('Auth Token:', authToken);

// للتحقق من استجابة API
fetch('/api/v1/auth/me', {
    headers: { 'Authorization': `Bearer ${token}` }
}).then(response => console.log(response.status));
```
