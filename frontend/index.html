<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تطبيق الدردشة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .auth-section, .chat-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .hidden {
            display: none;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background-color: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #007bff;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 تطبيق الدردشة الفورية</h1>
        
        <!-- قسم تسجيل الدخول -->
        <div id="authSection" class="auth-section">
            <h2>تسجيل الدخول / إنشاء حساب</h2>
            
            <div>
                <h3>إنشاء حساب جديد</h3>
                <input type="text" id="regUsername" placeholder="اسم المستخدم">
                <input type="email" id="regEmail" placeholder="البريد الإلكتروني">
                <input type="password" id="regPassword" placeholder="كلمة المرور">
                <button onclick="register()">إنشاء حساب</button>
            </div>
            
            <hr>
            
            <div>
                <h3>تسجيل الدخول</h3>
                <input type="text" id="loginUsername" placeholder="اسم المستخدم">
                <input type="password" id="loginPassword" placeholder="كلمة المرور">
                <button onclick="login()">تسجيل الدخول</button>
            </div>
        </div>
        
        <!-- قسم الدردشة -->
        <div id="chatSection" class="chat-section hidden">
            <h2>مرحباً <span id="currentUser"></span>! 👋</h2>
            
            <div>
                <h3>الرسائل</h3>
                <div id="messages" class="messages"></div>
                
                <div>
                    <input type="text" id="messageInput" placeholder="اكتب رسالتك هنا..." style="width: 60%;">
                    <input type="text" id="recipientInput" placeholder="اسم المستقبل" style="width: 20%;">
                    <button onclick="sendMessage()" style="width: 15%;">إرسال</button>
                </div>
            </div>
            
            <button onclick="logout()" style="background-color: #dc3545;">تسجيل الخروج</button>
        </div>
        
        <!-- منطقة الحالة -->
        <div id="status"></div>
    </div>

    <script>
        // متغيرات عامة
        let authToken = null;
        let websocket = null;
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // عرض رسالة حالة
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => statusDiv.innerHTML = '', 5000);
        }
        
        // تسجيل مستخدم جديد
        async function register() {
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            
            if (!username || !email || !password) {
                showStatus('يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus(`تم إنشاء الحساب بنجاح! مرحباً ${data.username}`);
                    // مسح الحقول
                    document.getElementById('regUsername').value = '';
                    document.getElementById('regEmail').value = '';
                    document.getElementById('regPassword').value = '';
                } else {
                    const error = await response.json();
                    showStatus(`خطأ: ${error.detail}`, 'error');
                }
            } catch (error) {
                showStatus(`خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        // تسجيل الدخول
        async function login() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!username || !password) {
                showStatus('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    
                    // إخفاء قسم تسجيل الدخول وإظهار قسم الدردشة
                    document.getElementById('authSection').classList.add('hidden');
                    document.getElementById('chatSection').classList.remove('hidden');
                    document.getElementById('currentUser').textContent = username;
                    
                    showStatus('تم تسجيل الدخول بنجاح!');
                    
                    // الاتصال بـ WebSocket
                    connectWebSocket();
                } else {
                    const error = await response.json();
                    showStatus(`خطأ في تسجيل الدخول: ${error.detail}`, 'error');
                }
            } catch (error) {
                showStatus(`خطأ في الاتصال: ${error.message}`, 'error');
            }
        }
        
        // الاتصال بـ WebSocket
        function connectWebSocket() {
            websocket = new WebSocket(`ws://localhost:8000/ws?token=${authToken}`);
            
            websocket.onopen = function(event) {
                showStatus('تم الاتصال بالخادم للرسائل الفورية!');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                displayMessage(data);
            };
            
            websocket.onclose = function(event) {
                showStatus('تم قطع الاتصال مع الخادم', 'error');
            };
            
            websocket.onerror = function(error) {
                showStatus('خطأ في الاتصال الفوري', 'error');
            };
        }
        
        // إرسال رسالة
        function sendMessage() {
            const messageText = document.getElementById('messageInput').value;
            const recipient = document.getElementById('recipientInput').value;
            
            if (!messageText.trim()) {
                showStatus('يرجى كتابة رسالة', 'error');
                return;
            }
            
            const message = {
                type: 'direct_message',
                content: messageText,
                recipient_username: recipient || null
            };
            
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify(message));
                document.getElementById('messageInput').value = '';
                showStatus('تم إرسال الرسالة');
            } else {
                showStatus('لا يوجد اتصال بالخادم', 'error');
            }
        }
        
        // عرض رسالة
        function displayMessage(data) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            
            const time = new Date().toLocaleTimeString('ar-SA');
            messageDiv.innerHTML = `
                <strong>${data.sender || 'مجهول'}:</strong> ${data.content || data.message}
                <small style="float: left; color: #666;">${time}</small>
            `;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        // تسجيل الخروج
        function logout() {
            authToken = null;
            if (websocket) {
                websocket.close();
            }
            
            document.getElementById('authSection').classList.remove('hidden');
            document.getElementById('chatSection').classList.add('hidden');
            document.getElementById('messages').innerHTML = '';
            
            // مسح حقول تسجيل الدخول
            document.getElementById('loginUsername').value = '';
            document.getElementById('loginPassword').value = '';
            
            showStatus('تم تسجيل الخروج');
        }
        
        // إرسال رسالة عند الضغط على Enter
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
