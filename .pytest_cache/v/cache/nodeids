["tests/test_auth.py::TestAuth::test_get_current_user", "tests/test_auth.py::TestAuth::test_get_current_user_invalid_token", "tests/test_auth.py::TestAuth::test_get_current_user_no_token", "tests/test_auth.py::TestAuth::test_login_success", "tests/test_auth.py::TestAuth::test_login_wrong_password", "tests/test_auth.py::TestAuth::test_login_wrong_username", "tests/test_auth.py::TestAuth::test_logout", "tests/test_auth.py::TestAuth::test_register_duplicate_email", "tests/test_auth.py::TestAuth::test_register_duplicate_username", "tests/test_auth.py::TestAuth::test_register_user", "tests/test_auth.py::TestAuth::test_register_weak_password", "tests/test_messages.py::TestMessages::test_create_direct_message", "tests/test_messages.py::TestMessages::test_create_message_nonexistent_recipient", "tests/test_messages.py::TestMessages::test_create_message_to_self", "tests/test_messages.py::TestMessages::test_create_message_without_auth", "tests/test_messages.py::TestMessages::test_get_direct_messages", "tests/test_messages.py::TestMessages::test_get_direct_messages_pagination", "tests/test_messages.py::TestMessages::test_update_message_status", "tests/test_messages.py::TestMessages::test_update_message_status_unauthorized", "tests/test_rooms.py::TestRooms::test_create_room", "tests/test_rooms.py::TestRooms::test_create_room_duplicate_name", "tests/test_rooms.py::TestRooms::test_get_my_rooms", "tests/test_rooms.py::TestRooms::test_get_nonexistent_room", "tests/test_rooms.py::TestRooms::test_get_room_by_id", "tests/test_rooms.py::TestRooms::test_get_room_members", "tests/test_rooms.py::TestRooms::test_get_rooms", "tests/test_rooms.py::TestRooms::test_get_rooms_with_search", "tests/test_rooms.py::TestRooms::test_join_nonexistent_room", "tests/test_rooms.py::TestRooms::test_join_room", "tests/test_rooms.py::TestRooms::test_leave_room", "tests/test_rooms.py::TestRooms::test_leave_room_not_member"]