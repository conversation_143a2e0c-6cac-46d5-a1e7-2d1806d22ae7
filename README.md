# Real-Time Chat Application Backend

A comprehensive real-time chat application backend built with FastAPI, PostgreSQL, and WebSocket support.

## Features

- **User Authentication**: JWT-based authentication with registration and login
- **User Profiles**: User management with online/offline status tracking
- **Real-time Messaging**: WebSocket support for instant messaging
- **Direct Messages**: One-to-one private messaging
- **Chat Rooms**: Public and private chat rooms with member management
- **Message History**: Persistent message storage with pagination
- **Message Status**: Delivery and read status tracking
- **Comprehensive Logging**: Structured JSON logging for all activities
- **Admin Routes**: Administrative endpoints for user and room management

## Tech Stack

- **Backend Framework**: FastAPI
- **Database**: PostgreSQL with SQL<PERSON><PERSON>chemy (async)
- **Authentication**: JWT tokens with bcrypt password hashing
- **Real-time Communication**: WebSocket
- **Testing**: pytest with async support
- **Logging**: Structured JSON logging

## Project Structure

```
time-chat/
├── app/
│   ├── api/                    # API endpoints
│   │   ├── auth.py            # Authentication endpoints
│   │   ├── users.py           # User management endpoints
│   │   ├── messages.py        # Message endpoints
│   │   ├── rooms.py           # Room endpoints
│   │   └── router.py          # Main API router
│   ├── auth/                   # Authentication logic
│   │   ├── dependencies.py    # Auth dependencies
│   │   ├── jwt_handler.py     # JWT token handling
│   │   └── password.py        # Password hashing
│   ├── models/                 # Database models
│   │   ├── database.py        # Database configuration
│   │   ├── user.py           # User model
│   │   ├── message.py        # Message model
│   │   ├── room.py           # Room and membership models
│   │   └── schemas.py        # Pydantic schemas
│   ├── services/              # Business logic
│   │   ├── auth_service.py   # Authentication service
│   │   ├── user_service.py   # User service
│   │   ├── message_service.py # Message service
│   │   └── room_service.py   # Room service
│   ├── sockets/               # WebSocket handling
│   │   ├── connection_manager.py # Connection management
│   │   ├── websocket_handler.py  # Message handling
│   │   └── websocket_endpoint.py # WebSocket endpoint
│   └── utils/
│       └── logger.py          # Logging utilities
├── tests/                     # Test files
├── logs/                      # Log files
├── config.py                  # Configuration
├── main.py                    # Application entry point
├── requirements.txt           # Dependencies
└── README.md                  # This file
```

## Installation

### Prerequisites

- Python 3.8+
- PostgreSQL 12+
- pip or poetry

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd time-chat
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   # Database
   DATABASE_URL=postgresql+asyncpg://username:password@localhost/chatdb
   
   # JWT
   SECRET_KEY=your-secret-key-here
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30
   
   # Application
   APP_NAME=Time Chat API
   DEBUG=True
   ```

5. **Set up PostgreSQL database**
   ```sql
   CREATE DATABASE chatdb;
   CREATE USER chatuser WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE chatdb TO chatuser;
   ```

6. **Run the application**
   ```bash
   python main.py
   ```

The application will be available at `http://localhost:8000`

## API Documentation

Once the application is running, you can access:

- **Interactive API docs**: `http://localhost:8000/docs`
- **ReDoc documentation**: `http://localhost:8000/redoc`

## WebSocket Connection

Connect to WebSocket at: `ws://localhost:8000/ws?token=<your_jwt_token>`

### WebSocket Message Types

#### Send Message
```json
{
  "type": "chat_message",
  "data": {
    "content": "Hello, world!",
    "recipient_id": 2,  // For direct messages
    "room_id": 1        // For room messages
  }
}
```

#### Join Room
```json
{
  "type": "join_room",
  "data": {
    "room_id": 1
  }
}
```

#### Leave Room
```json
{
  "type": "leave_room",
  "data": {
    "room_id": 1
  }
}
```

#### Typing Indicator
```json
{
  "type": "typing",
  "data": {
    "is_typing": true,
    "recipient_id": 2,  // For direct messages
    "room_id": 1        // For room messages
  }
}
```

## Testing

### Run Tests
```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_auth.py
```

### Test Coverage

The test suite covers:
- Authentication (registration, login, logout)
- User management
- Message creation and retrieval
- Room management and membership
- WebSocket connections (basic)

## Logging

The application uses structured JSON logging with different log levels:

- **INFO**: General application events
- **WARNING**: Potential issues
- **ERROR**: Error conditions
- **DEBUG**: Detailed debugging information

Logs are written to:
- Console (formatted for development)
- `logs/app.log` (JSON format for production)

## Development

### Code Style
- Follow PEP 8
- Use type hints
- Write docstrings for functions and classes
- Keep functions focused and small

### Adding New Features
1. Create/update models in `app/models/`
2. Add business logic in `app/services/`
3. Create API endpoints in `app/api/`
4. Add tests in `tests/`
5. Update documentation

## Production Deployment

### Environment Variables
Set these environment variables for production:
```env
DEBUG=False
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/dbname
SECRET_KEY=strong-random-secret-key
```

### Database Migration
For production, consider using Alembic for database migrations:
```bash
pip install alembic
alembic init alembic
# Configure alembic.ini and create migrations
```

### Docker Deployment
Create a `Dockerfile` and `docker-compose.yml` for containerized deployment.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
