"""
Room tests
"""
import pytest
from app.models.room import RoomType, MemberRole


class TestRooms:
    """Test room endpoints"""
    
    @pytest.mark.asyncio
    async def test_create_room(self, client, auth_headers):
        """Test creating a room"""
        room_data = {
            "name": "Test Room",
            "description": "A test room for testing",
            "room_type": RoomType.PUBLIC.value
        }
        
        response = await client.post("/api/v1/rooms/", json=room_data, headers=auth_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["name"] == "Test Room"
        assert data["description"] == "A test room for testing"
        assert data["room_type"] == RoomType.PUBLIC.value
    
    @pytest.mark.asyncio
    async def test_create_room_duplicate_name(self, client, auth_headers, db_session):
        """Test creating room with duplicate name"""
        # Create first room
        from app.models.room import Room
        
        room = Room(
            name="Duplicate Room",
            description="First room",
            room_type=RoomType.PUBLIC,
            created_by=1
        )
        db_session.add(room)
        await db_session.commit()
        
        # Try to create second room with same name
        room_data = {
            "name": "Duplicate Room",
            "description": "Second room",
            "room_type": RoomType.PUBLIC.value
        }
        
        response = await client.post("/api/v1/rooms/", json=room_data, headers=auth_headers)
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_rooms(self, client, auth_headers):
        """Test getting rooms list"""
        response = await client.get("/api/v1/rooms/", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
    
    @pytest.mark.asyncio
    async def test_get_rooms_with_search(self, client, auth_headers, db_session):
        """Test getting rooms with search filter"""
        # Create test rooms
        from app.models.room import Room
        
        room1 = Room(
            name="Python Discussion",
            description="Talk about Python",
            room_type=RoomType.PUBLIC,
            created_by=1
        )
        
        room2 = Room(
            name="JavaScript Chat",
            description="Talk about JavaScript",
            room_type=RoomType.PUBLIC,
            created_by=1
        )
        
        db_session.add_all([room1, room2])
        await db_session.commit()
        
        # Search for Python
        response = await client.get("/api/v1/rooms/?search=Python", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["total"] >= 1
        # Check that returned rooms contain "Python" in name
        for item in data["items"]:
            assert "Python" in item["name"]
    
    @pytest.mark.asyncio
    async def test_get_room_by_id(self, client, auth_headers, db_session):
        """Test getting room by ID"""
        # Create test room
        from app.models.room import Room
        
        room = Room(
            name="Test Room",
            description="Test description",
            room_type=RoomType.PUBLIC,
            created_by=1
        )
        db_session.add(room)
        await db_session.commit()
        await db_session.refresh(room)
        
        response = await client.get(f"/api/v1/rooms/{room.id}", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == room.id
        assert data["name"] == "Test Room"
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_room(self, client, auth_headers):
        """Test getting nonexistent room"""
        response = await client.get("/api/v1/rooms/99999", headers=auth_headers)
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_join_room(self, client, auth_headers, db_session):
        """Test joining a room"""
        # Create test room
        from app.models.room import Room
        
        room = Room(
            name="Join Test Room",
            description="Room for join testing",
            room_type=RoomType.PUBLIC,
            created_by=2  # Different user created it
        )
        db_session.add(room)
        await db_session.commit()
        await db_session.refresh(room)
        
        response = await client.post(f"/api/v1/rooms/{room.id}/join", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["room_id"] == room.id
        assert data["role"] == MemberRole.MEMBER.value
    
    @pytest.mark.asyncio
    async def test_join_nonexistent_room(self, client, auth_headers):
        """Test joining nonexistent room"""
        response = await client.post("/api/v1/rooms/99999/join", headers=auth_headers)
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_leave_room(self, client, auth_headers, test_user, db_session):
        """Test leaving a room"""
        # Create test room and membership
        from app.models.room import Room, RoomMember
        
        room = Room(
            name="Leave Test Room",
            description="Room for leave testing",
            room_type=RoomType.PUBLIC,
            created_by=2
        )
        db_session.add(room)
        await db_session.flush()
        
        membership = RoomMember(
            user_id=test_user.id,
            room_id=room.id,
            role=MemberRole.MEMBER
        )
        db_session.add(membership)
        await db_session.commit()
        
        response = await client.post(f"/api/v1/rooms/{room.id}/leave", headers=auth_headers)
        assert response.status_code == 200
        assert "Successfully left" in response.json()["message"]
    
    @pytest.mark.asyncio
    async def test_leave_room_not_member(self, client, auth_headers, db_session):
        """Test leaving room when not a member"""
        # Create test room without membership
        from app.models.room import Room
        
        room = Room(
            name="Not Member Room",
            description="Room user is not member of",
            room_type=RoomType.PUBLIC,
            created_by=2
        )
        db_session.add(room)
        await db_session.commit()
        await db_session.refresh(room)
        
        response = await client.post(f"/api/v1/rooms/{room.id}/leave", headers=auth_headers)
        assert response.status_code == 404
        assert "not a member" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_my_rooms(self, client, auth_headers, test_user, db_session):
        """Test getting user's rooms"""
        # Create test room and membership
        from app.models.room import Room, RoomMember
        
        room = Room(
            name="My Room",
            description="User's room",
            room_type=RoomType.PUBLIC,
            created_by=test_user.id
        )
        db_session.add(room)
        await db_session.flush()
        
        membership = RoomMember(
            user_id=test_user.id,
            room_id=room.id,
            role=MemberRole.ADMIN
        )
        db_session.add(membership)
        await db_session.commit()
        
        response = await client.get("/api/v1/rooms/my-rooms", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        assert any(r["name"] == "My Room" for r in data)
    
    @pytest.mark.asyncio
    async def test_get_room_members(self, client, auth_headers, test_user, db_session):
        """Test getting room members"""
        # Create test room and membership
        from app.models.room import Room, RoomMember
        
        room = Room(
            name="Members Test Room",
            description="Room for members testing",
            room_type=RoomType.PUBLIC,
            created_by=test_user.id
        )
        db_session.add(room)
        await db_session.flush()
        
        membership = RoomMember(
            user_id=test_user.id,
            room_id=room.id,
            role=MemberRole.ADMIN
        )
        db_session.add(membership)
        await db_session.commit()
        
        response = await client.get(f"/api/v1/rooms/{room.id}/members", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        assert any(m["user_id"] == test_user.id for m in data)
