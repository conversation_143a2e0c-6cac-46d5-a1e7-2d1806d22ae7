"""
Message tests
"""
import pytest
from app.models.message import MessageType, MessageStatus


class TestMessages:
    """Test message endpoints"""
    
    @pytest.mark.asyncio
    async def test_create_direct_message(self, client, auth_headers, test_user2):
        """Test creating a direct message"""
        message_data = {
            "content": "Hello, this is a test message!",
            "message_type": MessageType.DIRECT.value,
            "recipient_id": test_user2.id
        }
        
        response = await client.post("/api/v1/messages/", json=message_data, headers=auth_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["content"] == "Hello, this is a test message!"
        assert data["message_type"] == MessageType.DIRECT.value
        assert data["recipient_id"] == test_user2.id
        assert data["status"] == MessageStatus.SENT.value
    
    @pytest.mark.asyncio
    async def test_create_message_to_self(self, client, auth_headers, test_user):
        """Test creating a message to self (should fail)"""
        message_data = {
            "content": "Message to myself",
            "message_type": MessageType.DIRECT.value,
            "recipient_id": test_user.id
        }
        
        response = await client.post("/api/v1/messages/", json=message_data, headers=auth_headers)
        assert response.status_code == 400
        assert "Cannot send message to yourself" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_create_message_nonexistent_recipient(self, client, auth_headers):
        """Test creating a message to nonexistent recipient"""
        message_data = {
            "content": "Message to nobody",
            "message_type": MessageType.DIRECT.value,
            "recipient_id": 99999
        }
        
        response = await client.post("/api/v1/messages/", json=message_data, headers=auth_headers)
        assert response.status_code == 404
        assert "Recipient not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_get_direct_messages(self, client, auth_headers, test_user, test_user2, db_session):
        """Test getting direct messages between users"""
        # First create some messages
        from app.models.message import Message
        
        # Message from test_user to test_user2
        message1 = Message(
            content="Hello from user 1",
            message_type=MessageType.DIRECT,
            sender_id=test_user.id,
            recipient_id=test_user2.id
        )
        
        # Message from test_user2 to test_user
        message2 = Message(
            content="Hello back from user 2",
            message_type=MessageType.DIRECT,
            sender_id=test_user2.id,
            recipient_id=test_user.id
        )
        
        db_session.add_all([message1, message2])
        await db_session.commit()
        
        # Get messages
        response = await client.get(f"/api/v1/messages/direct/{test_user2.id}", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["total"] == 2
        assert len(data["items"]) == 2
    
    @pytest.mark.asyncio
    async def test_get_direct_messages_pagination(self, client, auth_headers, test_user2):
        """Test pagination for direct messages"""
        response = await client.get(
            f"/api/v1/messages/direct/{test_user2.id}?page=1&size=10", 
            headers=auth_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert "pages" in data
    
    @pytest.mark.asyncio
    async def test_update_message_status(self, client, auth_headers, test_user, test_user2, db_session):
        """Test updating message status"""
        # Create a message from test_user2 to test_user
        from app.models.message import Message
        
        message = Message(
            content="Test message for status update",
            message_type=MessageType.DIRECT,
            sender_id=test_user2.id,
            recipient_id=test_user.id
        )
        
        db_session.add(message)
        await db_session.commit()
        await db_session.refresh(message)
        
        # Update status to READ
        response = await client.patch(
            f"/api/v1/messages/{message.id}/status?status={MessageStatus.READ.value}",
            headers=auth_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == MessageStatus.READ.value
    
    @pytest.mark.asyncio
    async def test_update_message_status_unauthorized(self, client, auth_headers, test_user, test_user2, db_session):
        """Test updating message status for message not sent to user"""
        # Create a message from test_user to test_user2 (not to current user)
        from app.models.message import Message
        
        message = Message(
            content="Test message",
            message_type=MessageType.DIRECT,
            sender_id=test_user.id,
            recipient_id=test_user2.id
        )
        
        db_session.add(message)
        await db_session.commit()
        await db_session.refresh(message)
        
        # Try to update status (should fail because current user is not recipient)
        response = await client.patch(
            f"/api/v1/messages/{message.id}/status?status={MessageStatus.READ.value}",
            headers=auth_headers
        )
        assert response.status_code == 403
    
    @pytest.mark.asyncio
    async def test_create_message_without_auth(self, client, test_user2):
        """Test creating message without authentication"""
        message_data = {
            "content": "Unauthorized message",
            "message_type": MessageType.DIRECT.value,
            "recipient_id": test_user2.id
        }
        
        response = await client.post("/api/v1/messages/", json=message_data)
        assert response.status_code == 403  # No authorization header
